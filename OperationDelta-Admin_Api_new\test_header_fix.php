<?php
/**
 * 请求头类型错误修复验证脚本
 */

// 引入必要的类
require_once __DIR__ . '/vendor/autoload.php';

use think\Request;

try {
    echo "=== 请求头类型错误修复验证 ===\n\n";
    
    // 创建一个模拟请求对象
    echo "1. 测试 Request::header() 方法...\n";
    
    // 模拟请求
    $request = Request::create('http://example.com/test', 'GET');
    
    // 测试 User-Agent 头（字符串默认值）
    $userAgent = $request->header('User-Agent', 'Unknown');
    echo "   ✅ User-Agent 获取成功: {$userAgent}\n";
    
    // 测试 Content-Length 头（字符串默认值，然后转换为整数）
    $contentLength = $request->header('Content-Length', '0');
    $contentLengthInt = (int)$contentLength;
    echo "   ✅ Content-Length 获取成功: {$contentLength} (转换为整数: {$contentLengthInt})\n";
    
    // 测试条件判断
    if ($contentLengthInt > 0) {
        echo "   - Content-Length 大于 0，会记录请求大小\n";
    } else {
        echo "   - Content-Length 为 0 或未设置，不记录请求大小\n";
    }
    
    echo "\n2. 测试带有实际 Content-Length 的请求...\n";
    
    // 创建带有 Content-Length 头的请求
    $requestWithLength = Request::create('http://example.com/test', 'POST', [], [], [], [
        'HTTP_CONTENT_LENGTH' => '1024'
    ]);
    
    $contentLength2 = $requestWithLength->header('Content-Length', '0');
    $contentLengthInt2 = (int)$contentLength2;
    echo "   ✅ Content-Length 获取成功: {$contentLength2} (转换为整数: {$contentLengthInt2})\n";
    
    if ($contentLengthInt2 > 0) {
        echo "   - Content-Length 大于 0，会记录请求大小: {$contentLengthInt2} bytes\n";
    }
    
    echo "\n=== 所有测试通过 ===\n";
    echo "✅ 请求头类型错误已修复，header() 方法调用正常！\n";
    
} catch (TypeError $e) {
    echo "❌ 类型错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
} catch (Exception $e) {
    echo "❌ 其他错误: " . $e->getMessage() . "\n";
}
