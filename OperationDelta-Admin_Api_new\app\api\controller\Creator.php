<?php

namespace app\api\controller;

use app\common\controller\Frontend;
use think\facade\Db;
use think\Response;
use think\facade\Cache;

/**
 * 内容创作者接口
 */
class Creator extends Frontend
{
    // 无需登录的接口
    protected array $noNeedLogin = ['index', 'detail', 'recommend', 'categories'];

    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 获取创作者列表
     *
     * @param int $page 页码
     * @param int $limit 每页记录数
     * @param string $category 分类
     * @param string $keyword 搜索关键词
     * @param int $creator_type 创作者类型：1=直播主播, 2=视频博主, 3=两者皆是
     * @return Response
     */
    public function index()
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 12);
        $category = $this->request->param('category/s', '');
        $keyword = $this->request->param('keyword/s', '');
        $creator_type = $this->request->param('creator_type/d', 0); // 0表示全部类型
        $status = $this->request->param('status/d', ''); // 状态筛选

        // 缓存key设计，包含所有影响结果的参数
        $cacheKey = "creator_index:page={$page}&limit={$limit}&category={$category}&keyword={$keyword}&creator_type={$creator_type}&status={$status}";
        $cacheTtl = 6000; // 缓存60秒
        if (Cache::has($cacheKey)) {
            return json(Cache::get($cacheKey));
        }

        $where = [];
        $where[] = ['delete_time', '=', null];

        if ($category) {
            $where[] = ['category', '=', $category];
        }

        if ($keyword) {
            $where[] = ['nickname', 'like', "%{$keyword}%"];
        }

        if ($creator_type > 0) {
            if ($creator_type == 1) {
                $where[] = ['creator_type', 'in', [1, 3]]; // 直播主播 + 两者皆是
            } elseif ($creator_type == 2) {
                $where[] = ['creator_type', 'in', [2, 3]]; // 视频博主 + 两者皆是
            } else {
                $where[] = ['creator_type', '=', $creator_type];
            }
        }

        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }

        // 使用模型查询以利用自动类型转换
        $total = \app\admin\model\sjz\Creators::where($where)->count();
        $list = \app\admin\model\sjz\Creators::where($where)
            ->page($page, $limit)
            ->order('sort_order desc, id desc')
            ->select()
            ->toArray();

        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'total' => $total,
                'list' => $list,
                'current_page' => intval($page),
                'per_page' => intval($limit),
                'last_page' => ceil($total / $limit)
            ]
        ];
        Cache::set($cacheKey, $result, $cacheTtl);
        return json($result);
    }

    /**
     * 获取创作者详情
     *
     * @param int $id 数据库ID
     * @return Response
     */
    public function detail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        $cacheKey = "creator_detail:id={$id}";
        $cacheTtl = 6000; // 缓存5分钟
        if (Cache::has($cacheKey)) {
            return json(Cache::get($cacheKey));
        }

        $creator = Db::name('sjz_creators')
            ->where('id', $id)
            ->where('delete_time', null)
            ->find();

        if (!$creator) {
            return json(['code' => 0, 'msg' => '创作者不存在']);
        }

        // 处理游戏标签
        if (!empty($creator['game_tags'])) {
            $creator['game_tags_array'] = explode(',', $creator['game_tags']);
        } else {
            $creator['game_tags_array'] = [];
        }

        // 处理多平台视频链接
        if (!empty($creator['video_platforms'])) {
            $creator['video_platforms'] = json_decode($creator['video_platforms'], true);
        } else {
            $creator['video_platforms'] = [];
        }

        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => $creator
        ];
        Cache::set($cacheKey, $result, $cacheTtl);
        return json($result);
    }

    /**
     * 获取推荐创作者
     *
     * @param int $limit 限制数量
     * @param int $creator_type 创作者类型：1=直播主播, 2=视频博主, 3=两者皆是
     * @return Response
     */
    public function recommend()
    {
        $limit = $this->request->param('limit/d', 5);
        $creator_type = $this->request->param('creator_type/d', 0); // 0表示全部类型
        $cacheKey = "creator_recommend:limit={$limit}&creator_type={$creator_type}";
        $cacheTtl = 6000; // 缓存60秒
        if (Cache::has($cacheKey)) {
            return json(Cache::get($cacheKey));
        }

        $where = [
            ['is_recommend', '=', 1],
            ['delete_time', '=', null]
        ];

        if ($creator_type > 0) {
            if ($creator_type == 1) {
                $where[] = ['creator_type', 'in', [1, 3]]; // 直播主播 + 两者皆是
            } elseif ($creator_type == 2) {
                $where[] = ['creator_type', 'in', [2, 3]]; // 视频博主 + 两者皆是
            } else {
                $where[] = ['creator_type', '=', $creator_type];
            }
        }

        $list = Db::name('sjz_creators')
            ->where($where)
            ->order('sort_order desc, id desc')
            ->limit($limit)
            ->select()
            ->toArray();

        foreach ($list as &$item) {
            // 处理游戏标签
            if (!empty($item['game_tags'])) {
                $item['game_tags_array'] = explode(',', $item['game_tags']);
            } else {
                $item['game_tags_array'] = [];
            }

            // 处理多平台视频链接
            if (!empty($item['video_platforms'])) {
                $item['video_platforms'] = json_decode($item['video_platforms'], true);
            } else {
                $item['video_platforms'] = [];
            }
        }

        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => $list
        ];
        Cache::set($cacheKey, $result, $cacheTtl);
        return json($result);
    }

    /**
     * 获取创作者分类列表
     *
     * @return Response
     */
    public function categories()
    {
        $cacheKey = "creator_categories";
        $cacheTtl = 3000; // 缓存10分钟
        if (Cache::has($cacheKey)) {
            return json(Cache::get($cacheKey));
        }

        // 从数据库中获取所有分类
        $categories = Db::name('sjz_creators')
            ->where('delete_time', null)
            ->where('category', '<>', '')
            ->field('DISTINCT category')
            ->select()
            ->column('category');

        $result = [
            'code' => 1,
            'msg' => '获取成功',
            'data' => $categories
        ];
        Cache::set($cacheKey, $result, $cacheTtl);
        return json($result);
    }
}