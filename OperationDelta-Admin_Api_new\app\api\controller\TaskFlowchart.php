<?php

namespace app\api\controller;

use app\common\controller\Frontend;
use think\Response;
use think\facade\Db;
use think\exception\ValidateException;

/**
 * 任务流程图控制器
 */
class TaskFlowchart extends Frontend
{
    // 所有接口均不需要登录
    protected array $noNeedLogin = ['index', 'detail', 'save', 'update', 'verifyEditPassword'];
    
    /**
     * 获取流程图列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            $season = $this->request->param('season/s', '');
            $status = $this->request->param('status/s', 'active');
            
            $query = Db::name('sjz_task_flowcharts')
                ->where('status', $status);
            
            if ($season) {
                $query->where('season', $season);
            }
            
            $list = $query->order('sort_order', 'asc')
                ->order('id', 'desc')
                ->select();
            
            // 处理JSON数据
            foreach ($list as &$item) {
                $item['nodes_data'] = json_decode($item['nodes_data'], true) ?: [];
                $item['edges_data'] = json_decode($item['edges_data'], true) ?: [];
            }
            
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'list' => $list,
                    'seasons' => ['S5', 'S4', 'S3', 'S2', 'S1']
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取流程图详情
     * @return Response
     */
    public function detail(): Response
    {
        try {
            $id = $this->request->param('id/d', 0);
            
            if (!$id) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }
            
            $flowchart = Db::name('sjz_task_flowcharts')
                ->where('id', $id)
                ->find();
            
            if (!$flowchart) {
                return json(['code' => 0, 'msg' => '流程图不存在']);
            }
            
            // 处理JSON数据
            $flowchart['nodes_data'] = json_decode($flowchart['nodes_data'], true) ?: [];
            $flowchart['edges_data'] = json_decode($flowchart['edges_data'], true) ?: [];
            
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $flowchart
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 保存流程图（新建）
     * @return Response
     */
    public function save(): Response
    {
        try {
            // 验证管理员权限（这里简化处理，实际应该通过后台权限系统）
            if (!$this->checkAdminAuth()) {
                return json(['code' => 0, 'msg' => '无权限操作']);
            }
            
            $data = $this->request->post();
            
            // 验证必填字段
            $validate = \think\facade\Validate::rule([
                'title' => 'require|max:200',
                'season' => 'require|in:S1,S2,S3,S4,S5',
                'nodes_data' => 'require',
                'edges_data' => 'require'
            ])->message([
                'title.require' => '标题不能为空',
                'title.max' => '标题不能超过200个字符',
                'season.require' => '赛季不能为空',
                'season.in' => '赛季格式错误',
                'nodes_data.require' => '节点数据不能为空',
                'edges_data.require' => '边数据不能为空'
            ]);
            
            if (!$validate->check($data)) {
                return json(['code' => 0, 'msg' => $validate->getError()]);
            }
            
            // 处理数据
            $insertData = [
                'title' => $data['title'],
                'description' => $data['description'] ?? '',
                'season' => $data['season'],
                'nodes_data' => is_array($data['nodes_data']) ? json_encode($data['nodes_data']) : $data['nodes_data'],
                'edges_data' => is_array($data['edges_data']) ? json_encode($data['edges_data']) : $data['edges_data'],
                'status' => $data['status'] ?? 'active',
                'sort_order' => $data['sort_order'] ?? 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            $id = Db::name('sjz_task_flowcharts')->insertGetId($insertData);
            
            return json([
                'code' => 1,
                'msg' => '保存成功',
                'data' => ['id' => $id]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '保存失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新流程图
     * @return Response
     */
    public function update(): Response
    {
        try {
            $id = $this->request->param('id/d', 0);
            $data = $this->request->post();
            
            if (!$id) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }
            
            // 检查流程图是否存在
            $exists = Db::name('sjz_task_flowcharts')->where('id', $id)->find();
            if (!$exists) {
                return json(['code' => 0, 'msg' => '流程图不存在']);
            }
            
            // 验证数据
            $validate = \think\facade\Validate::rule([
                'title' => 'max:200',
                'season' => 'in:S1,S2,S3,S4,S5'
            ])->message([
                'title.max' => '标题不能超过200个字符',
                'season.in' => '赛季格式错误'
            ]);
            
            if (!$validate->check($data)) {
                return json(['code' => 0, 'msg' => $validate->getError()]);
            }
            
            // 准备更新数据
            $updateData = ['update_time' => date('Y-m-d H:i:s')];
            
            if (isset($data['title'])) {
                $updateData['title'] = $data['title'];
            }
            if (isset($data['description'])) {
                $updateData['description'] = $data['description'];
            }
            if (isset($data['season'])) {
                $updateData['season'] = $data['season'];
            }
            if (isset($data['nodes_data'])) {
                $updateData['nodes_data'] = is_array($data['nodes_data']) ? json_encode($data['nodes_data']) : $data['nodes_data'];
            }
            if (isset($data['edges_data'])) {
                $updateData['edges_data'] = is_array($data['edges_data']) ? json_encode($data['edges_data']) : $data['edges_data'];
            }
            if (isset($data['status'])) {
                $updateData['status'] = $data['status'];
            }
            if (isset($data['sort_order'])) {
                $updateData['sort_order'] = intval($data['sort_order']);
            }
            
            Db::name('sjz_task_flowcharts')->where('id', $id)->update($updateData);
            
            return json([
                'code' => 1,
                'msg' => '更新成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 验证编辑模式密码
     * @return Response
     */
    public function verifyEditPassword(): Response
    {
        try {
            $password = $this->request->param('password/s', '');

            if (empty($password)) {
                return json([
                    'code' => 0,
                    'msg' => '密码不能为空',
                    'data' => ['valid' => false]
                ]);
            }

            // 编辑模式密码（建议从配置文件读取或使用环境变量）
            $correctPassword = 'dfhub1admin2025'; // 这里可以改为更安全的密码

            // 验证密码
            $isValid = ($password === $correctPassword);

            return json([
                'code' => 1,
                'msg' => $isValid ? '密码验证成功' : '密码错误',
                'data' => ['valid' => $isValid]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '验证失败：' . $e->getMessage(),
                'data' => ['valid' => false]
            ]);
        }
    }

    /**
     * 检查管理员权限（简化版本）
     * @return bool
     */
    private function checkAdminAuth(): bool
    {
        // 这里应该实现真正的权限检查
        // 例如检查请求头中的管理员token等
        // 暂时返回true，实际使用时需要完善
        return true;
    }
}