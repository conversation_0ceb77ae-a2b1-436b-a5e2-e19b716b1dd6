<?php
declare(strict_types=1);

namespace app\api\service;

use think\Response;

class ResponseAdapter
{
    /**
     * 保持与原有Api基类完全一致的响应格式
     */
    public static function success(string $msg = '', mixed $data = null, int $code = 1): Response
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'time' => request()->server('REQUEST_TIME'), // 保持原有时间格式
            'data' => $data,
        ]);
    }
    
    public static function error(string $msg = '', mixed $data = null, int $code = 0): Response
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'time' => request()->server('REQUEST_TIME'),
            'data' => $data,
        ]);
    }
    
    /**
     * 统一结果返回方法
     */
    public static function result(string $msg, mixed $data = null, int $code = 0): Response
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'time' => request()->server('REQUEST_TIME'),
            'data' => $data,
        ]);
    }
    
    /**
     * 分页响应格式
     */
    public static function paginate(array $items, int $total, int $page, int $pageSize, string $msg = '获取成功'): Response
    {
        return self::success($msg, [
            'items' => $items,
            'total' => $total,
            'pagination' => [
                'current' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'pages' => ceil($total / $pageSize)
            ]
        ]);
    }
    
    /**
     * 列表响应格式（兼容现有格式）
     */
    public static function list(array $data, string $msg = '获取成功'): Response
    {
        return self::success($msg, $data);
    }
    
    /**
     * 详情响应格式
     */
    public static function detail(mixed $data, string $msg = '获取成功'): Response
    {
        return self::success($msg, $data);
    }
    
    /**
     * 缓存响应格式（带缓存标识）
     */
    public static function cached(mixed $data, string $cacheType = '', string $msg = '获取成功'): Response
    {
        $message = !empty($cacheType) ? $msg . '(缓存)' : $msg;
        return self::success($message, $data);
    }
    
    /**
     * 验证错误响应
     */
    public static function validationError(string $msg = '参数验证失败', array $errors = []): Response
    {
        return self::error($msg, $errors, 400);
    }
    
    /**
     * 权限错误响应
     */
    public static function forbidden(string $msg = '权限不足'): Response
    {
        return self::error($msg, null, 403);
    }
    
    /**
     * 资源不存在响应
     */
    public static function notFound(string $msg = '资源不存在'): Response
    {
        return self::error($msg, null, 404);
    }
    
    /**
     * 服务器错误响应
     */
    public static function serverError(string $msg = '服务器内部错误'): Response
    {
        return self::error($msg, null, 500);
    }
    
    /**
     * 兼容原有响应格式的方法（保持100%兼容性）
     */
    public static function compatible(string $msg, mixed $data = null, int $code = 1): Response
    {
        // 完全保持与原有Api控制器的响应格式一致
        return json([
            'code' => $code,
            'msg' => $msg,
            'time' => request()->server('REQUEST_TIME'),
            'data' => $data,
        ]);
    }
}