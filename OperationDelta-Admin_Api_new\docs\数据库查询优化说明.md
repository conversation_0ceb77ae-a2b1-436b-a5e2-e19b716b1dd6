# 数据库查询性能优化说明

## 🔍 **问题分析**

### 原始性能问题
从日志分析发现的主要性能瓶颈：

1. **循环单个查询**：`RankingService.php` 中对每个物品执行单独的价格查询
2. **重复查询模式**：每个物品查询4次（当前、1天前、7天前、30天前）
3. **查询耗时过长**：单个查询耗时 58-76ms，总体响应时间过长
4. **索引利用不充分**：现有索引无法完全覆盖查询需求

### 性能数据对比
- **优化前**：每个物品 4 次单独查询 × N 个物品 = 4N 次数据库查询
- **优化后**：4 次批量查询（不论物品数量）= 4 次数据库查询

## 🚀 **优化方案**

### 1. **代码层面优化**

#### A. 批量查询替代循环查询
**文件**: `app/api/service/RankingService.php`

**优化前**:
```php
foreach ($items as &$item) {
    $objectId = (int)$item['object_id'];
    $currentPrice = $this->getLatestPrice($objectId);        // 单个查询
    $dayPrice = $this->getPriceBeforeDays($objectId, 1);     // 单个查询
    $weekPrice = $this->getPriceBeforeDays($objectId, 7);    // 单个查询
    $monthPrice = $this->getPriceBeforeDays($objectId, 30);  // 单个查询
}
```

**优化后**:
```php
$objectIds = array_column($items, 'object_id');
// 批量获取所有价格数据
$currentPrices = $this->getBatchLatestPrices($objectIds);      // 1次批量查询
$dayPrices = $this->getBatchPricesBeforeDays($objectIds, 1);   // 1次批量查询
$weekPrices = $this->getBatchPricesBeforeDays($objectIds, 7);  // 1次批量查询
$monthPrices = $this->getBatchPricesBeforeDays($objectIds, 30);// 1次批量查询

foreach ($items as &$item) {
    $objectId = (int)$item['object_id'];
    $currentPrice = $currentPrices[$objectId] ?? 0.0;  // 内存查找
    $dayPrice = $dayPrices[$objectId] ?? 0.0;          // 内存查找
    // ...
}
```

#### B. 新增批量查询方法

1. **`getBatchLatestPrices()`**: 批量获取最新价格
2. **`getBatchLatestPricesFromHistory()`**: 从历史表批量获取最新价格
3. **`getBatchPricesBeforeDays()`**: 批量获取指定天数前的价格

#### C. 优化的SQL查询
使用高效的子查询和JOIN来减少查询次数：

```sql
SELECT ph1.object_id, ph1.price 
FROM ba_sjz_price_history ph1
INNER JOIN (
    SELECT object_id, MAX(timestamp) as max_timestamp
    FROM ba_sjz_price_history 
    WHERE object_id IN (?,?,?) AND price > 0
    GROUP BY object_id
) ph2 ON ph1.object_id = ph2.object_id AND ph1.timestamp = ph2.max_timestamp
WHERE ph1.price > 0
```

### 2. **数据库层面优化**

#### A. 索引优化
**文件**: `database/optimize_price_indexes.sql`

创建了4个针对性的索引：

1. **`idx_object_timestamp_desc`**: `(object_id, timestamp DESC)`
   - 用途：快速获取单个物品的最新价格
   - 优化查询：`ORDER BY timestamp DESC LIMIT 1`

2. **`idx_object_price_timestamp`**: `(object_id, price, timestamp DESC)`
   - 用途：过滤价格 > 0 的查询
   - 优化查询：`WHERE price > 0 ORDER BY timestamp DESC`

3. **`idx_timestamp_object`**: `(timestamp, object_id)`
   - 用途：时间范围查询
   - 优化查询：`WHERE timestamp BETWEEN ? AND ?`

4. **`idx_object_timestamp_price`**: `(object_id, timestamp DESC, price)`
   - 用途：覆盖索引，减少回表查询
   - 优化查询：包含所有需要的字段，避免回表

#### B. 表结构优化建议

现有表结构已经比较合理：
- 主键：`id` (自增)
- 唯一索引：`uk_object_timestamp` (object_id, timestamp)
- 普通索引：`idx_object_id` (object_id)

## 📊 **性能提升预期**

### 查询次数优化
- **优化前**：100个物品 × 4次查询 = 400次数据库查询
- **优化后**：4次批量查询 = 4次数据库查询
- **提升比例**：减少 99% 的查询次数

### 响应时间优化
- **优化前**：400次 × 60ms = 24秒
- **优化后**：4次 × 80ms = 320ms
- **提升比例**：响应时间减少 98.7%

### 数据库负载优化
- **连接数使用**：大幅减少数据库连接占用时间
- **锁竞争**：减少表锁和行锁的竞争
- **缓存命中率**：提高MySQL查询缓存命中率

## 🛠️ **部署步骤**

### 1. 执行数据库索引优化
```bash
mysql -u username -p database_name < database/optimize_price_indexes.sql
```

### 2. 验证索引创建
```sql
SHOW INDEX FROM ba_sjz_price_history;
```

### 3. 测试查询性能
```sql
EXPLAIN SELECT price FROM ba_sjz_price_history 
WHERE object_id = 1001 AND price > 0 
ORDER BY timestamp DESC LIMIT 1;
```

### 4. 监控性能改善
- 观察API响应时间
- 检查数据库慢查询日志
- 监控服务器资源使用情况

## 📈 **监控指标**

### 关键性能指标 (KPI)
1. **API响应时间**：目标 < 500ms
2. **数据库查询时间**：目标 < 50ms
3. **并发处理能力**：提升 10-20 倍
4. **服务器CPU使用率**：预期降低 30-50%

### 监控方法
1. **性能监控系统**：已集成的性能监控会自动记录改善情况
2. **数据库监控**：观察慢查询日志的变化
3. **应用监控**：通过日志分析响应时间变化

## 🔄 **后续优化建议**

### 1. 缓存策略
- 对热点价格数据实施Redis缓存
- 缓存时间：5-10分钟
- 缓存键设计：`price:latest:{object_id}`

### 2. 读写分离
- 价格查询使用只读从库
- 价格更新使用主库
- 减少主库查询压力

### 3. 数据分区
- 按时间分区历史价格表
- 提高历史数据查询性能
- 便于数据归档和清理

### 4. 异步处理
- 价格变化计算异步化
- 使用消息队列处理批量更新
- 提高实时查询响应速度

## ✅ **验证清单**

- [ ] 数据库索引已创建
- [ ] 代码优化已部署
- [ ] 性能测试已通过
- [ ] 监控指标正常
- [ ] 无功能回归问题
- [ ] 用户体验改善明显
