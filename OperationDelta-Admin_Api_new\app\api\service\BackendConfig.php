<?php
declare(strict_types=1);

namespace app\api\service;

use think\facade\Config;

/**
 * 后端配置管理类
 * 提供便捷的配置访问方法
 */
class BackendConfig
{
    /**
     * 获取缓存配置
     */
    public static function cache(string $key = '', mixed $default = null): mixed
    {
        $configKey = empty($key) ? 'system.cache' : "system.cache.{$key}";
        return Config::get($configKey, $default);
    }

    /**
     * 检查缓存是否启用
     */
    public static function isCacheEnabled(string $type = ''): bool
    {
        // 检查全局缓存开关
        if (!self::cache('enable', true)) {
            return false;
        }

        // 检查特定类型的缓存开关
        if (!empty($type)) {
            return self::cache("types.{$type}.enable", true);
        }

        return true;
    }
}
