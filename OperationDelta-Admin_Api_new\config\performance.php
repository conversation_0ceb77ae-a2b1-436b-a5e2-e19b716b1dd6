<?php
/**
 * API性能监控配置
 */
return [
    // 是否启用性能监控
    'enabled' => true,
    
    // 监控级别: debug, info, warning, error  
    'log_level' => 'warning',  // 只记录警告和错误
    
    // 慢查询阈值设置(秒)
    'slow_query_thresholds' => [
        'get_list_data' => 0.5,        // 列表查询
        'get_total_count' => 0.3,      // 计数查询
        'process_price_changes' => 0.2, // 价格处理
        'cache_operation' => 0.05,     // 缓存操作
        'total_request' => 2.0,        // 总请求时间
    ],
    
    // 内存使用警告阈值
    'memory_thresholds' => [
        'warning' => 50 * 1024 * 1024,  // 50MB
        'critical' => 100 * 1024 * 1024, // 100MB
    ],
    
    // 缓存命中率阈值
    'cache_hit_rate_threshold' => 80, // 低于80%发出警告
    
    // 性能等级评估标准
    'performance_levels' => [
        'excellent' => ['duration' => 0.1, 'memory' => 5 * 1024 * 1024],   // 100ms, 5MB
        'good' => ['duration' => 0.5, 'memory' => 20 * 1024 * 1024],       // 500ms, 20MB
        'fair' => ['duration' => 1.0, 'memory' => 50 * 1024 * 1024],       // 1s, 50MB
        'poor' => ['duration' => 3.0, 'memory' => 100 * 1024 * 1024],      // 3s, 100MB
    ],
    
    // 是否记录SQL语句(仅开发环境建议开启)
    'log_sql' => false,
    
    // SQL语句最大长度
    'max_sql_length' => 200,
    
    // 是否启用详细的缓存统计
    'detailed_cache_stats' => true,
    
    // 监控的API接口列表(空数组表示监控所有)
    'monitored_apis' => [
        // 指定监控的接口，可以是 'ControllerName::methodName' 或 'methodName'
        // 'Ranking::getRankingList',
        // 'getKeycardRanking', 
        // 'getBulletRanking',
    ],
    
    // 跳过监控的动作列表
    'skip_actions' => ['ping', 'health', 'status', 'test'],
    
    // 跳过监控的控制器列表
    'skip_controllers' => ['Error', 'Miss'],
    
    // 性能报告保留天数
    'report_retention_days' => 7,
    
    // 是否启用实时性能告警
    'enable_alerts' => true,
    
    // 告警通知配置
    'alert_config' => [
        // 连续慢查询次数达到此值时告警
        'slow_query_count_threshold' => 5,
        
        // 内存使用率超过此值时告警  
        'memory_usage_threshold' => 90,
        
        // 缓存命中率低于此值时告警
        'low_cache_hit_rate_threshold' => 70,
        
        // 数据库查询次数过多告警阈值
        'max_db_queries' => 10,
    ],
    
    // 是否启用详细统计(会增加日志量)
    'detailed_stats' => false,
    
    // 异步日志配置
    'async_logging' => true,
    
    // 日志批处理大小
    'log_batch_size' => 10,
    
    // 采样率(百分比，减少监控频率)
    'sampling_rate' => 10,  // 只监控10%的请求
    
    // 敏感参数过滤配置
    'sensitive_params' => ['password', 'token', 'secret', 'key', 'auth', 'authorization', 'jwt'],
];
