<?php

namespace app\api\controller;

use Throwable;
use ba\Captcha;
use ba\ClickCaptcha;
use think\facade\Config;
use app\common\facade\Token;
use app\common\controller\Frontend;
use app\api\validate\User as UserValidate;
use app\common\model\user\LoginLog;

class User extends Frontend
{
    protected array $noNeedLogin = ['checkIn', 'logout'];

    protected array $noNeedPermission = ['index'];

    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 记录登录日志
     * @param int $userId 用户ID
     * @param string $username 用户名
     * @param string $status 状态 1成功 0失败
     * @param string $failureReason 失败原因
     */
    protected function recordLoginLog(int $userId, string $username, string $status, string $failureReason = ''): void
    {
        try {
            $userAgent = $this->request->header('User-Agent');
            LoginLog::create([
                'user_id' => $userId,
                'username' => $username,
                'statu' => $status,
                'ip' => $this->request->ip(),
                'device' => $this->getDeviceType($userAgent),
                'os' => $this->getOS($userAgent),
                'browser' => $this->getBrowser($userAgent),
                'useragent' => $userAgent,
                'failure_reason' => $failureReason,
                'login_time' => time(),
                'create_time' => time()
            ]);
        } catch (Throwable $e) {
            \think\facade\Log::error('记录登录日志失败：' . $e->getMessage());
        }
    }

    /**
     * 会员签入(登录和注册)
     * @throws Throwable
     */
    public function checkIn(): void
    {
        $openMemberCenter = Config::get('buildadmin.open_member_center');
        if (!$openMemberCenter) {
            $this->error(__('Member center disabled'));
        }

        // 检查登录态
        if ($this->auth->isLogin()) {
            $this->success(__('You have already logged in. There is no need to log in again~'), [
                'type' => $this->auth::LOGGED_IN
            ], $this->auth::LOGIN_RESPONSE_CODE);
        }

        $userLoginCaptchaSwitch = Config::get('buildadmin.user_login_captcha');

        if ($this->request->isPost()) {
            $params = $this->request->post(['tab', 'email', 'mobile', 'username', 'password', 'keep', 'captcha', 'captchaId', 'captchaInfo', 'registerType']);

            // 提前检查 tab ，然后将以 tab 值作为数据验证场景
            if (!in_array($params['tab'] ?? '', ['login', 'register'])) {
                $this->error(__('Unknown operation'));
            }

            $validate = new UserValidate();
            try {
                $validate->scene($params['tab'])->check($params);
            } catch (Throwable $e) {
                if ($params['tab'] == 'login') {
                    $this->recordLoginLog(0, $params['username'] ?? '', '0', $e->getMessage());
                }
                $this->error($e->getMessage());
            }

            if ($params['tab'] == 'login') {
                if ($userLoginCaptchaSwitch) {
                    $captchaObj = new ClickCaptcha();
                    if (!$captchaObj->check($params['captchaId'], $params['captchaInfo'])) {
                        $this->recordLoginLog(0, $params['username'], '0', '验证码错误');
                        $this->error(__('Captcha error'));
                    }
                }
                $res = $this->auth->login($params['username'], $params['password'], !empty($params['keep']));
            } elseif ($params['tab'] == 'register') {
                $captchaObj = new Captcha();
                if (!$captchaObj->check($params['captcha'], $params[$params['registerType']] . 'user_register')) {
                    $this->error(__('Please enter the correct verification code'));
                }
                $res = $this->auth->register($params['username'], $params['password'], $params['mobile'], $params['email']);
            }

            if (isset($res) && $res === true) {
                if ($params['tab'] == 'login') {
                    // 登录成功，记录日志
                    $userInfo = $this->auth->getUserInfo();
                    $this->recordLoginLog($userInfo['id'], $params['username'], '1');
                }
                
                $this->success(__('Login succeeded!'), [
                    'userInfo'  => $this->auth->getUserInfo(),
                    'routePath' => '/user'
                ]);
            } else {
                $msg = $this->auth->getError();
                $msg = $msg ?: __('Check in failed, please try again or contact the website administrator~');
                
                if ($params['tab'] == 'login') {
                    // 登录失败，记录日志
                    $this->recordLoginLog(0, $params['username'], '0', $msg);
                }
                
                $this->error($msg);
            }
        }

        $this->success('', [
            'userLoginCaptchaSwitch'  => $userLoginCaptchaSwitch,
            'accountVerificationType' => get_account_verification_type()
        ]);
    }

    public function logout(): void
    {
        if ($this->request->isPost()) {
            $refreshToken = $this->request->post('refreshToken', '');
            if ($refreshToken) {
                Token::delete((string)$refreshToken);
            }
            
            // 记录注销日志
            if ($this->auth->isLogin()) {
                $userInfo = $this->auth->getUserInfo();
                $this->recordLoginLog($userInfo['id'], $userInfo['username'], '1', '用户注销');
            }
            
            $this->auth->logout();
            $this->success();
        }
    }

    /**
     * 获取设备类型
     */
    private function getDeviceType(string $userAgent): string
    {
        if (preg_match('/(iPad|iPhone|iPod)/', $userAgent)) {
            return 'iOS';
        } elseif (preg_match('/Android/', $userAgent)) {
            return 'Android';
        } elseif (preg_match('/(Windows|Linux|Mac)/', $userAgent)) {
            return 'Desktop';
        }
        return 'Unknown';
    }

    /**
     * 获取操作系统
     */
    private function getOS(string $userAgent): string
    {
        if (preg_match('/Windows NT 10.0/', $userAgent)) {
            return 'Windows 10';
        } elseif (preg_match('/Windows NT 6.3/', $userAgent)) {
            return 'Windows 8.1';
        } elseif (preg_match('/Windows NT 6.2/', $userAgent)) {
            return 'Windows 8';
        } elseif (preg_match('/Windows NT 6.1/', $userAgent)) {
            return 'Windows 7';
        } elseif (preg_match('/Mac OS X/', $userAgent)) {
            return 'MacOS';
        } elseif (preg_match('/Linux/', $userAgent)) {
            return 'Linux';
        } elseif (preg_match('/iPhone OS/', $userAgent)) {
            return 'iOS';
        } elseif (preg_match('/Android/', $userAgent)) {
            return 'Android';
        }
        return 'Unknown';
    }

    /**
     * 获取浏览器类型
     */
    private function getBrowser(string $userAgent): string
    {
        if (preg_match('/MSIE/i', $userAgent)) {
            return 'Internet Explorer';
        } elseif (preg_match('/Firefox/i', $userAgent)) {
            return 'Firefox';
        } elseif (preg_match('/Chrome/i', $userAgent)) {
            return 'Chrome';
        } elseif (preg_match('/Safari/i', $userAgent)) {
            return 'Safari';
        } elseif (preg_match('/Opera/i', $userAgent)) {
            return 'Opera';
        } elseif (preg_match('/Edge/i', $userAgent)) {
            return 'Edge';
        }
        return 'Unknown';
    }
}