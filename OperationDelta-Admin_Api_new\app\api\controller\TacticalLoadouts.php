<?php

namespace app\api\controller;

use think\facade\Cache;
use app\common\controller\Frontend;
use app\api\service\CacheManager;
use think\Response;
use think\App;

/**
 * 战术装备推荐API控制器
 * 用于读取Redis中存储的战术装备数据
 */
class TacticalLoadouts extends Frontend
{
    protected array $noNeedLogin = ['index', 'debug', 'testKey', 'maps', 'levels', 'allMapLevels', 'availableMapIds', 'availableLevels'];
    protected array $noNeedPermission = [];

    // Redis相关常量 - 更新为实际的unified版本键前缀
    private const REDIS_KEY_PREFIX = 'tactical_unified:';
    
    private CacheManager $cacheManager;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cacheManager = new CacheManager();
    }

    /**
     * 获取Redis键名 - 更新为unified版本的键格式
     */
    private function getRedisKey(string $mapId, string $level): string
    {
        return self::REDIS_KEY_PREFIX . $mapId . ':' . $level;
    }

    /**
     * 从Redis获取数据
     */
    private function getFromRedis(string $key): ?array
    {
        try {
            $data = Cache::store('redis')->get($key);
            if ($data === null || $data === false) {
                return null;
            }

            // 如果数据是JSON字符串，尝试解码
            if (is_string($data)) {
                // 先使用默认深度解码
                $decoded = json_decode($data, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 如果失败，再提升解析深度重试
                    $decoded = json_decode($data, true, 2048);
                }
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $decoded;
                }
                // 如果JSON解码仍然失败，返回null
                return null;
            }

            // 如果已经是数组，直接返回
            if (is_array($data)) {
                return $data;
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 递归确保所有字符串为有效UTF-8，防止json_encode报错
     */
    private function utf8ize(mixed $data): mixed
    {
        if (is_array($data)) {
            foreach ($data as $k => $v) {
                $data[$k] = $this->utf8ize($v);
            }
            return $data;
        } elseif (is_string($data)) {
            // 若不是合法UTF-8，iconv 将去除非法字节
            if (!mb_check_encoding($data, 'UTF-8')) {
                $data = iconv('UTF-8', 'UTF-8//IGNORE', $data);
            }
            return $data;
        }
        return $data;
    }

    /**
     * 清理装备数据 - unified版本数据已经是清理过的格式
     */
    private function cleanLoadoutData(array $data): array
    {
        // unified版本的数据已经是清理过的字典格式，无需额外处理
        // 只需要确保数据结构完整性
        if (isset($data['loadouts']) && is_array($data['loadouts'])) {
            foreach ($data['loadouts'] as &$loadout) {
                // 确保所有必要字段存在
                $loadout['total_cost'] = $loadout['total_cost'] ?? 0;
                $loadout['budget_ratio'] = $loadout['budget_ratio'] ?? 0;
                $loadout['generated_at'] = $loadout['generated_at'] ?? null;

                // 确保装备字段存在
                $equipmentTypes = ['weapon', 'helmet', 'armor', 'backpack', 'chest_rig'];
                foreach ($equipmentTypes as $type) {
                    if (!isset($loadout[$type])) {
                        $loadout[$type] = null;
                    }
                }

                // 确保配件数组存在
                if (!isset($loadout['accessories']) || !is_array($loadout['accessories'])) {
                    $loadout['accessories'] = [];
                }
            }
        }

        return $data;
    }

    /**
     * 解析物品数据数组
     */
    private function parseItemData(array $itemArray): ?array
    {
        if (empty($itemArray)) {
            return null;
        }

        $result = [];

        // 第一个元素通常是ItemInfoV3对象字符串
        if (isset($itemArray[0]) && is_string($itemArray[0])) {
            $itemInfo = $this->parseItemInfoString($itemArray[0]);
            if ($itemInfo) {
                $result = array_merge($result, $itemInfo);
            }
        }

        // 第二个元素通常是PriceDataV3对象字符串
        if (isset($itemArray[1]) && is_string($itemArray[1])) {
            $priceInfo = $this->parsePriceDataString($itemArray[1]);
            if ($priceInfo) {
                $result = array_merge($result, $priceInfo);
            }
        }

        // 第三个元素通常是平均价格
        if (isset($itemArray[2]) && is_numeric($itemArray[2])) {
            $result['avg_price'] = (float)$itemArray[2];
        }

        return empty($result) ? null : $result;
    }

    /**
     * 解析ItemInfoV3对象字符串
     */
    private function parseItemInfoString(string $itemString): ?array
    {
        // 使用正则表达式提取ItemInfoV3的字段
        $pattern = '/ItemInfoV3\(([^)]+)\)/';
        if (!preg_match($pattern, $itemString, $matches)) {
            return null;
        }

        $fields = $matches[1];
        $result = [];

        // 解析各个字段
        $fieldPatterns = [
            'object_id' => '/object_id=(\d+)/',
            'object_name' => '/object_name=\'([^\']+)\'/',
            'category_id' => '/category_id=(\d+)/',
            'grade' => '/grade=(\d+)/',
            'length' => '/length=(\d+)/',
            'width' => '/width=(\d+)/',
            'weight' => '/weight=([\d.]+)/',
            'pic' => '/pic=\'([^\']+)\'/',
            'pre_pic' => '/pre_pic=\'([^\']+)\'/',
            'desc' => '/desc=\'([^\']*)\'/',
            'primary_class' => '/primary_class=\'([^\']+)\'/',
            'second_class_cn' => '/second_class_cn=\'([^\']+)\'/'
        ];

        foreach ($fieldPatterns as $field => $pattern) {
            if (preg_match($pattern, $fields, $fieldMatches)) {
                $value = $fieldMatches[1];
                // 转换数值类型
                if (in_array($field, ['object_id', 'category_id', 'grade', 'length', 'width'])) {
                    $result[$field] = (int)$value;
                } elseif ($field === 'weight') {
                    $result[$field] = (float)$value;
                } else {
                    $result[$field] = $value;
                }
            }
        }

        return $result;
    }

    /**
     * 解析PriceDataV3对象字符串
     */
    private function parsePriceDataString(string $priceString): ?array
    {
        // 使用正则表达式提取PriceDataV3的字段
        $pattern = '/PriceDataV3\(([^)]+)\)/';
        if (!preg_match($pattern, $priceString, $matches)) {
            return null;
        }

        $fields = $matches[1];
        $result = [];

        // 解析价格相关字段
        $fieldPatterns = [
            'current_price' => '/current_price=([\d.]+)/',
            'price_24h_ago' => '/price_24h_ago=([\d.]+)/',
            'weekly_avg_price' => '/weekly_avg_price=([\d.]+)/',
            'price_change_24h' => '/price_change_24h=([-\d.]+)/',
            'price_change_percent' => '/price_change_percent=([-\d.]+)/',
            'has_price_data' => '/has_price_data=(True|False)/'
        ];

        foreach ($fieldPatterns as $field => $pattern) {
            if (preg_match($pattern, $fields, $fieldMatches)) {
                $value = $fieldMatches[1];
                if ($field === 'has_price_data') {
                    $result[$field] = $value === 'True';
                } else {
                    $result[$field] = (float)$value;
                }
            }
        }

        return $result;
    }

    /**
     * 动态获取所有可用的地图ID - 更新为unified版本
     * @return array 返回去重后的地图ID数组
     */
    private function getAvailableMapIds(): array
    {
        // 直接使用原生 Redis 连接以便执行 keys 操作
        $redis = Cache::store('redis')->handler();
        // 构造匹配模式: tactical_unified:*
        $pattern = self::REDIS_KEY_PREFIX . '*';
        // 获取所有匹配的键
        $keys = $redis->keys($pattern);
        $mapIds = [];
        foreach ($keys as $key) {
            // 键格式示例: tactical_unified:dam:机密
            $parts = explode(':', $key);
            if (count($parts) >= 3) {
                // 第2段为地图ID (索引1)
                $mapIds[] = $parts[1];
            }
        }
        // 去重并返回
        return array_values(array_unique($mapIds));
    }

    /**
     * 动态获取指定地图的所有预算等级名称 - 更新为unified版本
     * @param string $mapId 地图ID
     * @return array 返回去重后的等级名称数组
     */
    private function getAvailableLevels(string $mapId): array
    {
        $redis = Cache::store('redis')->handler();
        // 匹配模式: tactical_unified:{mapId}:*
        $pattern = self::REDIS_KEY_PREFIX . "{$mapId}:*";
        $keys = $redis->keys($pattern);
        $levels = [];
        foreach ($keys as $key) {
            // 键格式示例: tactical_unified:dam:机密
            $parts = explode(':', $key);
            if (count($parts) >= 3) {
                // 第3段为等级名称 (索引2)
                $levels[] = $parts[2];
            }
        }
        return array_values(array_unique($levels));
    }

    /**
     * 获取指定地图所有等级的数据 - 更新为unified版本
     */
    private function getAllMapLevels(string $mapId): array
    {
        $result = [];

        // 动态获取该地图的所有等级
        $availableLevels = $this->getAvailableLevels($mapId);

        foreach ($availableLevels as $level) {
            // 生成键: tactical_unified:{mapId}:{level}
            $key = $this->getRedisKey($mapId, $level);

            $data = $this->getFromRedis($key);
            if ($data) {
                // 清理并加入结果
                $cleanedData = $this->cleanLoadoutData($data);
                $result[$level] = $cleanedData;
            }
        }

        return $result;
    }

    /**
     * 获取所有地图的战术装备数据
     * GET /api/tactical_loadouts/index
     * 
     * 参数:
     * - map_id: 地图ID，可选
     * - level: 预算等级，可选
     * - formatted: 是否格式化数据，默认true
     */
    public function index(): Response
    {
        try {
            $mapId = $this->request->param('map_id', '');
            $level = $this->request->param('level', '');
            $formatted = $this->request->param('formatted', 'true') === 'true';

            // 直接扫描所有 unified 装备键
            $redis = Cache::store('redis')->handler();
            $pattern = self::REDIS_KEY_PREFIX . '*';
            $keys = $redis->keys($pattern);

            // 如果没有任何键，直接返回提示
            if (empty($keys)) {
                return json([
                    'code' => 0,
                    'msg'  => '暂无装备数据',
                    'data' => []
                ], 200, [], ['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE]);
            }

            $dataList = [];
            $filteredKeys = [];

            // 过滤符合条件的键 - 更新为unified版本的键格式
            foreach ($keys as $k) {
                $parts = explode(':', $k);
                if (count($parts) >= 3) {
                    $keyMapId = $parts[1];  // 地图ID在索引1
                    $keyLevel = $parts[2];  // 等级在索引2

                    // 根据参数过滤
                    if ($mapId && $keyMapId !== $mapId) continue;
                    if ($level && $keyLevel !== $level) continue;

                    $filteredKeys[] = $k;
                }
            }

            foreach ($filteredKeys as $k) {
                // 获取原始字符串
                $raw = $redis->get($k);

                // 尝试JSON解码
                $decoded = null;
                if (is_string($raw)) {
                    $decoded = json_decode($raw, true);
                }

                if ($decoded && $formatted) {
                    // 清理和格式化数据
                    $cleanedData = $this->cleanLoadoutData($decoded);
                    $formattedData = $this->formatLoadoutData($cleanedData);
                    
                    $dataList[] = [
                        'key' => $k,
                        'map_id' => explode(':', $k)[1] ?? '',  // 地图ID在索引1
                        'level' => explode(':', $k)[2] ?? '',   // 等级在索引2
                        'data' => $formattedData
                    ];
                } else {
                    $dataList[] = [
                        'key' => $k,
                        'map_id' => explode(':', $k)[1] ?? '',  // 地图ID在索引1
                        'level' => explode(':', $k)[2] ?? '',   // 等级在索引2
                        'data' => $decoded
                    ];
                }
            }

            return json([
                'code' => 1,
                'msg'  => '获取成功',
                'data' => $dataList,
                'total' => count($dataList)
            ], 200, [], ['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE | JSON_PARTIAL_OUTPUT_ON_ERROR]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg'  => '获取失败: ' . $e->getMessage(),
                'data' => []
            ], 200, [], ['json_encode_param' => JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE]);
        }
    }

    /**
     * 调试方法 - 查看Redis中的实际键
     * GET /api/tactical_loadouts/debug
     */
    public function debug(): Response
    {
        try {
            $redis = Cache::store('redis')->handler();

            // 获取所有键
            $allKeys = $redis->keys('*');

            // 过滤出可能相关的键
            $tacticalKeys = [];
            $otherKeys = [];

            foreach ($allKeys as $key) {
                if (strpos($key, 'tactical') !== false) {
                    $tacticalKeys[] = $key;
                } else {
                    $otherKeys[] = $key;
                }
            }

            return json([
                'code' => 1,
                'msg' => '调试信息',
                'data' => [
                    'total_keys' => count($allKeys),
                    'tactical_keys' => $tacticalKeys,
                    'other_keys_sample' => $otherKeys,
                    'current_hour' => date('Ymd_H'),
                    'current_prefix' => self::REDIS_KEY_PREFIX
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '调试失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 格式化装备数据 - 更新为unified版本
     */
    private function formatLoadoutData($data): array
    {
        if (!isset($data['loadouts']) || !is_array($data['loadouts'])) {
            return $data;
        }

        $formattedLoadouts = [];

        foreach ($data['loadouts'] as $loadout) {
            $formattedLoadout = [
                'weapon' => $this->formatEquipmentItem($loadout['weapon'] ?? null),
                'accessories' => [],
                'helmet' => $this->formatEquipmentItem($loadout['helmet'] ?? null),
                'armor' => $this->formatEquipmentItem($loadout['armor'] ?? null),
                'backpack' => $this->formatEquipmentItem($loadout['backpack'] ?? null),
                'chest_rig' => $this->formatEquipmentItem($loadout['chest_rig'] ?? null),
                'total_cost' => $loadout['total_cost'] ?? 0,
                'formatted_total_cost' => number_format($loadout['total_cost'] ?? 0, 0, '.', ','),
                'budget_ratio' => $loadout['budget_ratio'] ?? 0,
                'formatted_budget_ratio' => number_format(($loadout['budget_ratio'] ?? 0) * 100, 1) . '%',
                'generated_at' => $loadout['generated_at'] ?? null,
                'map_context' => $loadout['map_context'] ?? null
            ];

            // 格式化配件
            if (isset($loadout['accessories']) && is_array($loadout['accessories'])) {
                foreach ($loadout['accessories'] as $accessory) {
                    $formattedLoadout['accessories'][] = $this->formatEquipmentItem($accessory);
                }
            }

            $formattedLoadouts[] = $formattedLoadout;
        }

        return [
            'loadouts' => $formattedLoadouts,
            'generated_at' => $data['generated_at'] ?? null,
            'total_count' => $data['total_count'] ?? count($formattedLoadouts)
        ];
    }
    
    /**
     * 格式化单个装备项 - 更新为unified版本的数据字段
     */
    private function formatEquipmentItem($item): ?array
    {
        if (!$item) return null;

        return [
            'object_id' => $item['object_id'] ?? null,
            'name' => $item['name'] ?? '',
            'price' => $item['price'] ?? 0,
            'formatted_price' => number_format($item['price'] ?? 0, 0, '.', ','),
            'grade' => $item['grade'] ?? 0,
            'category' => $item['category'] ?? '',
            'item_info' => $item['item_info'] ?? null,
            'price_data' => $item['price_data'] ?? null
        ];
    }
    
    /**
     * 获取地图列表
     * GET /api/tactical_loadouts/maps
     */
    public function maps(): Response
    {
        try {
            $mapIds = $this->getAvailableMapIds();
            
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $mapIds
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取指定地图的预算等级列表
     * GET /api/tactical_loadouts/levels?map_id=dam
     */
    public function levels(): Response
    {
        try {
            $mapId = $this->request->param('map_id', '');
            if (empty($mapId)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供map_id参数',
                    'data' => []
                ]);
            }
            
            $levels = $this->getAvailableLevels($mapId);
            
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $levels
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '获取失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 测试特定键的数据
     * GET /api/tactical_loadouts/testKey?key=tactical_v3:map:dam:绝密:20250704_16
     */
    public function testKey(): Response
    {
        try {
            $key = $this->request->param('key', '');
            if (empty($key)) {
                return json([
                    'code' => 0,
                    'msg' => '请提供key参数',
                    'data' => []
                ]);
            }

            // 直接从Redis获取原始数据
            $redis = Cache::store('redis')->handler();
            $rawData = $redis->get($key);

            // 尝试通过Cache门面获取
            $cacheData = Cache::store('redis')->get($key);

            // 尝试JSON解码
            $decodedData = null;
            if (is_string($rawData)) {
                $decodedData = json_decode($rawData, true);
            }

            return json([
                'code' => 1,
                'msg' => '测试结果',
                'data' => [
                    'key' => $key,
                    'raw_data_type' => gettype($rawData),
                    'raw_data_length' => is_string($rawData) ? strlen($rawData) : 'N/A',
                    'cache_data_type' => gettype($cacheData),
                    'json_decode_success' => $decodedData !== null,
                    'json_error' => json_last_error_msg(),
                    'sample_data' => is_string($rawData) ? substr($rawData, 0, 500) : $rawData
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }
}
