# OperationDelta API 服务

<div align="center">
    <h1 style="font-size: 36px;color: #2c3e50;font-weight: 600;margin: 0 0 6px 0;">OperationDelta</h1>
    <p style="font-size: 17px;color: #6a8bad;margin-bottom: 10px;">游戏数据管理与价格监控API服务平台</p>
    <a href="./docs/api/Index.md">API文档</a> |
    <a href="#功能特性">功能特性</a> |
    <a href="#技术栈">技术栈</a> |
    <a href="#快速开始">快速开始</a>
</div>

<br />

<p align="center">
    <a href="https://www.thinkphp.cn/" target="_blank">
        <img src="https://img.shields.io/badge/ThinkPHP-8.1-brightgreen?color=91aac3&labelColor=439EFD" alt="thinkphp">
    </a>
    <a href="https://v3.vuejs.org/" target="_blank">
        <img src="https://img.shields.io/badge/Vue-3.5-brightgreen?color=91aac3&labelColor=439EFD" alt="vue">
    </a>
    <a href="https://element-plus.org/zh-CN/" target="_blank">
        <img src="https://img.shields.io/badge/Element--Plus-2.9-brightgreen?color=91aac3&labelColor=439EFD" alt="element plus">
    </a>
    <a href="https://www.tslang.cn/" target="_blank">
        <img src="https://img.shields.io/badge/TypeScript-5.7-blue?color=91aac3&labelColor=439EFD" alt="typescript">
    </a>
    <a href="https://vitejs.dev/" target="_blank">
        <img src="https://img.shields.io/badge/Vite-6.0-blue?color=91aac3&labelColor=439EFD" alt="vite">
    </a>
</p>

## 项目介绍

OperationDelta 是一个专业的游戏数据管理与价格监控API服务平台，基于 ThinkPHP8 + Vue3 + TypeScript 技术栈开发。主要提供游戏物品数据查询、价格历史跟踪、排行榜统计等核心功能，为游戏玩家和开发者提供完整的数据服务解决方案。

## 功能特性

### 🎮 游戏数据管理
- **物品管理**：完整的游戏物品数据库，支持多分类管理
- **价格追踪**：实时价格监控，历史价格图表展示
- **数据缓存**：智能缓存策略，提供高性能数据访问

### 📊 数据统计分析
- **价格排行榜**：多维度价格排行统计
- **趋势分析**：价格变动趋势分析和预测
- **数据导出**：支持多种格式的数据导出

### 🔐 用户系统
- **账户管理**：完整的用户注册、登录、资料管理
- **收藏功能**：用户可收藏关注的游戏物品
- **权限控制**：基于角色的访问控制系统

### 🚀 高性能架构
- **缓存策略**：Redis缓存，智能TTL管理
- **API优化**：RESTful API设计，支持批量查询
- **响应式设计**：自适应多端访问

## 技术栈

### 后端技术
- **框架**：ThinkPHP 8.1
- **数据库**：MySQL 8.0+
- **缓存**：Redis 6.0+
- **服务器**：Nginx + PHP-FPM

### 前端技术
- **框架**：Vue 3.5 + TypeScript 5.7
- **构建工具**：Vite 6.0
- **UI组件**：Element Plus 2.9
- **状态管理**：Pinia
- **图表库**：ECharts

### 开发工具
- **代码规范**：ESLint + Prettier
- **版本控制**：Git
- **API文档**：Markdown

## API接口

### 核心接口列表

| 接口模块 | 功能描述 | 文档链接 |
|---------|----------|----------|
| Items | 游戏物品数据查询、价格历史 | [Items.md](./docs/api/Items.md) |
| Account | 用户账户管理、收藏功能 | [Account.md](./docs/api/Account.md) |
| Ranking | 价格排行榜统计 | [Ranking.md](./docs/api/Ranking.md) |
| Index | 系统初始化、公告管理 | [Index.md](./docs/api/Index.md) |

### 接口特点
- **统一响应格式**：所有API采用统一的JSON响应格式
- **错误处理**：完善的错误码和错误信息处理
- **参数验证**：严格的请求参数验证
- **接口限流**：防止API滥用的限流机制

## 项目结构

```
OperationDelta-Admin_Api_new/
├── app/                          # 应用目录
│   ├── api/                      # API模块
│   │   ├── controller/           # 控制器
│   │   ├── service/              # 服务层
│   │   └── validate/             # 验证器
│   ├── common/                   # 公共模块
│   │   ├── model/                # 数据模型
│   │   ├── library/              # 类库
│   │   └── middleware/           # 中间件
│   └── admin/                    # 后台管理模块
├── web/                          # 前端项目
│   ├── src/                      # 源码目录
│   ├── public/                   # 静态资源
│   └── dist/                     # 构建输出
├── docs/                         # 项目文档
│   └── api/                      # API接口文档
├── config/                       # 配置文件
└── public/                       # Web入口
```

## 快速开始

### 环境要求
- PHP >= 8.1
- MySQL >= 8.0
- Redis >= 6.0
- Node.js >= 18.0
- Composer >= 2.0

## 配置说明

### 数据库配置
```php
// config/database.php
'connections' => [
    'mysql' => [
        'hostname' => 'localhost',
        'database' => 'operation_delta',
        'username' => 'root',
        'password' => '',
        'charset'  => 'utf8mb4',
    ],
],
```

### 缓存配置
```php
// config/cache.php
'stores' => [
    'redis' => [
        'type'   => 'redis',
        'host'   => '127.0.0.1',
        'port'   => 6379,
        'prefix' => 'od_',
    ],
],
```

## 开发指南

### API开发规范
- 遵循RESTful设计原则
- 使用统一的响应格式
- 实现完整的错误处理
- 添加详细的接口文档

### 代码规范
- 遵循PSR-4自动加载规范
- 使用类型声明和返回类型
- 编写单元测试
- 保持代码注释完整

## 部署说明

### 生产环境部署
1. 设置Nginx虚拟主机
2. 配置PHP-FPM
3. 设置Redis服务
4. 构建前端资源
5. 配置定时任务

### 性能优化
- 开启OPcache
- 配置Redis持久化
- 使用CDN加速静态资源
- 开启Gzip压缩

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。

### 提交规范
- 使用清晰的提交信息
- 遵循代码规范
- 包含必要的测试
- 更新相关文档

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 [Issue](../../issues)
- 发送邮件至项目维护者

---

<div align="center">
    <p>如果这个项目对你有帮助，请给个 ⭐ Star 支持一下！</p>
</div>