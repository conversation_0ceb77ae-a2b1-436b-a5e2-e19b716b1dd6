# API性能监控系统使用说明

## 📊 概述

本系统为所有API控制器提供自动化的性能监控功能，通过中间件自动收集各项性能指标并输出到日志中。

## 🚀 已启用功能

### 1. **自动性能监控**
- ✅ **中间件自动监控** - 所有API接口自动启用性能监控
- ✅ **基础控制器集成** - 提供便捷的性能监控方法
- ✅ **配置化管理** - 通过配置文件控制监控行为

### 2. **监控指标**
- ⏱️ **响应时间** - 总耗时、各阶段耗时分析
- 💾 **内存使用** - 内存消耗、峰值内存监控  
- 🗄️ **数据库查询** - 查询次数、耗时、效率统计
- 🏃 **缓存性能** - 缓存命中率、操作耗时
- 📈 **业务指标** - 返回记录数、分页信息等

## 📁 文件结构

```
app/api/
├── middleware/
│   └── PerformanceMiddleware.php     # 性能监控中间件
├── service/  
│   └── PerformanceMonitor.php        # 性能监控核心类
├── controller/
│   ├── BaseApiController.php         # API基础控制器
│   └── Ranking.php                   # 示例：已集成性能监控
└── config/
    └── performance.php               # 性能监控配置
```

## ⚙️ 配置说明

### 基础配置 (`app/api/config/performance.php`)

```php
return [
    // 是否启用性能监控
    'enabled' => true,
    
    // 监控级别: debug, info, warning, error  
    'log_level' => 'info',
    
    // 慢查询阈值设置(秒)
    'slow_query_thresholds' => [
        'get_list_data' => 0.5,
        'get_total_count' => 0.3, 
        'total_request' => 2.0,
    ],
    
    // 内存使用警告阈值
    'memory_thresholds' => [
        'warning' => 50 * 1024 * 1024,  // 50MB
    ],
    
    // 跳过监控的接口
    'skip_actions' => ['ping', 'health', 'status'],
    
    // 是否启用详细统计
    'detailed_stats' => true,
];
```

## 🔧 控制器集成

### 方法1：继承BaseApiController（推荐）

```php
use app\api\controller\BaseApiController;

class YourController extends BaseApiController 
{
    public function yourAction()
    {
        // 自动启用性能监控
        
        // 可选：添加自定义指标
        $this->recordMetric('custom_metric', $value, 'unit');
        
        // 可选：监控数据库查询
        $result = $this->executeQuery(function() {
            return Db::name('table')->select();  
        }, 'custom_query');
        
        // 使用统一响应格式（会自动抛出HttpResponseException）
        $this->success('操作成功', $result);
    }
}
```

### 方法2：直接使用中间件

无需修改现有控制器，中间件会自动监控所有请求。

## 📋 日志输出示例

### 性能报告日志
```log
[性能监控] 最终报告 {
  "api_name": "Ranking::getRankingList",
  "request_id": "perf_649a1b2c3d4e5f", 
  "total_duration": 0.245,
  "memory_used": "8.5MB",
  "performance_level": "良好",
  "cache_hit_rate": 85.5,
  "timers": {
    "param_validation": {"duration": 0.001, "percentage": 0.4},
    "cache_lookup": {"duration": 0.025, "percentage": 10.2},
    "get_list_data": {"duration": 0.156, "percentage": 63.7}
  },
  "counters": {"db_queries": 2, "cache_hits": 15}
}
```

### 数据库查询日志
```log
[性能监控] 数据库查询 {
  "request_id": "perf_649a1b2c3d4e5f",
  "query_type": "list_data_query", 
  "duration": "0.156s",
  "record_count": 20,
  "queries_per_second": 128.21
}
```

### 性能告警日志
```log
[性能告警] API性能异常 {
  "api": "Ranking::getRankingList",
  "alerts": [
    "API响应时间过长: 2.3s (阈值: 2.0s)",
    "内存使用过高: 65.2MB (阈值: 50.0MB)"
  ],
  "performance_level": "较差"
}
```

## 🎯 性能等级评估

系统自动评估API性能等级：

- **优秀** - 耗时 < 100ms，内存 < 5MB
- **良好** - 耗时 < 500ms，内存 < 20MB  
- **一般** - 耗时 < 1s，内存 < 50MB
- **较差** - 耗时 < 3s，内存 < 100MB
- **差** - 超过以上标准

## 🔍 监控控制

### 启用/禁用监控
```php
// 在配置中控制
'enabled' => false,  // 完全禁用

// 或跳过特定接口  
'skip_actions' => ['health', 'ping'],
```

### 自定义监控
```php
// 在控制器中添加自定义指标
$this->recordMetric('business_records', $count, 'records');
$this->recordMetric('processing_stage', 'data_validation');

// 监控特定操作耗时
$this->startTimer('data_processing');
// ... 业务逻辑
$duration = $this->endTimer('data_processing');
```

## 📊 性能优化建议

根据日志输出的性能数据，可以进行针对性优化：

1. **慢查询优化** - 关注 `timers` 中耗时最长的环节
2. **缓存优化** - 提高 `cache_hit_rate` 缓存命中率
3. **内存优化** - 关注 `memory_used` 和 `peak_memory`
4. **数据库优化** - 减少 `db_queries` 查询次数

## 🛠 故障排查

### 常见问题

1. **没有日志输出**
   - 检查 `performance.enabled` 是否为 true
   - 检查接口是否在 `skip_actions` 列表中

2. **性能数据不准确**
   - 确认中间件优先级设置正确
   - 检查是否有其他中间件干扰

3. **日志量过大**
   - 设置 `detailed_stats => false`
   - 调整 `log_level` 级别

## 📈 性能分析

通过日志分析工具（如ELK、Splunk等）可以：

- 📊 **趋势分析** - API性能变化趋势
- 🔍 **瓶颈定位** - 找出性能瓶颈环节  
- ⚠️ **异常监控** - 及时发现性能异常
- 📋 **报表生成** - 生成性能分析报表

---

## 💡 提示

- 性能监控数据会自动记录到应用日志中
- 建议配置日志轮转，避免日志文件过大
- 生产环境可适当调整监控精度以减少性能开销
- 可结合APM工具做更深入的性能分析