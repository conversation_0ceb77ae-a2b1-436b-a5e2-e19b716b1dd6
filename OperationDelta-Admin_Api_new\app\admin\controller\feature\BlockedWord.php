<?php
/*
 * @Description: 屏蔽词管理控制器
 * @Author: AI
 * @Date: 2023-05-27
 */

namespace app\admin\controller\feature;

use app\common\controller\Backend;
use app\common\model\sjz\BlockedWords;
use app\api\service\WordFilterService;
use think\db\exception\PDOException;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\Response;
use fast\Tree;

/**
 * 屏蔽词管理
 */
class BlockedWord extends Backend
{
    /**
     * BlockedWords模型对象
     * @var object
     */
    protected $model = null;

    protected $preExcludeFields = ['create_time', 'update_time'];

    protected $quickSearchField = ['word', 'category'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new BlockedWords();
    }

    /**
     * 查看
     */
    public function index(): Response
    {
        if ($this->request->param('select')) {
            $this->select();
        }

        list($where, $alias, $limit, $order) = $this->buildparams();
        $res = $this->model
            ->where($where)
            ->order($order)
            ->paginate($limit);

        return $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 添加
     */
    public function add(): Response
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                return $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data = $this->excludeFields($data);
            $result = false;
            Db::startTrans();
            try {
                $result = $this->model->save($data);
                Db::commit();
                
                // 清除屏蔽词缓存
                $wordFilter = new WordFilterService();
                $wordFilter->clearCache();
            } catch (ValidateException $e) {
                Db::rollback();
                return $this->error($e->getMessage());
            } catch (PDOException | Exception $e) {
                Db::rollback();
                return $this->error($e->getMessage());
            }
            if ($result !== false) {
                return $this->success(__('Added successfully'));
            } else {
                return $this->error(__('No rows were added'));
            }
        }

        return $this->success();
    }

    /**
     * 编辑
     */
    public function edit($id = null): Response
    {
        $row = $this->model->find($id);
        if (!$row) {
            return $this->error(__('Record not found'));
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                return $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data = $this->excludeFields($data);
            $result = false;
            Db::startTrans();
            try {
                $result = $row->save($data);
                Db::commit();
                
                // 清除屏蔽词缓存
                $wordFilter = new WordFilterService();
                $wordFilter->clearCache();
            } catch (ValidateException $e) {
                Db::rollback();
                return $this->error($e->getMessage());
            } catch (PDOException | Exception $e) {
                Db::rollback();
                return $this->error($e->getMessage());
            }
            if ($result !== false) {
                return $this->success(__('Update successful'));
            } else {
                return $this->error(__('No rows updated'));
            }
        }

        return $this->success('', [
            'row' => $row
        ]);
    }

    /**
     * 删除
     * @param null $ids
     */
    public function del($ids = null): Response
    {
        if (!$this->request->isDelete() || !$ids) {
            return $this->error(__('Parameter error'));
        }

        $where = [];
        $list = $this->model->where($where)->where('id', 'in', $ids)->select();
        if (!$list) {
            return $this->error(__('No rows were deleted'));
        }

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
            
            // 清除屏蔽词缓存
            $wordFilter = new WordFilterService();
            $wordFilter->clearCache();
        } catch (PDOException | Exception $e) {
            Db::rollback();
            return $this->error($e->getMessage());
        }

        if ($count) {
            return $this->success(__('Deleted %s records', [$count]));
        } else {
            return $this->error(__('No rows were deleted'));
        }
    }
    
    /**
     * 批量添加
     */
    public function batchAdd(): Response
    {
        if ($this->request->isPost()) {
            $words = $this->request->post('words');
            $category = $this->request->post('category', 'general');
            $is_regex = $this->request->post('is_regex', 0);
            
            if (!$words) {
                return $this->error(__('Words cannot be empty'));
            }
            
            // 拆分词语
            $wordList = preg_split('/[\r\n,，;；]+/', $words);
            $wordList = array_filter(array_map('trim', $wordList));
            
            if (empty($wordList)) {
                return $this->error(__('No valid words found'));
            }
            
            $count = 0;
            Db::startTrans();
            try {
                foreach ($wordList as $word) {
                    // 检查是否已存在
                    $exist = $this->model->where('word', $word)->find();
                    if (!$exist) {
                        $this->model->create([
                            'word' => $word,
                            'category' => $category,
                            'is_regex' => $is_regex
                        ]);
                        $count++;
                    }
                }
                Db::commit();
                
                // 清除屏蔽词缓存
                $wordFilter = new WordFilterService();
                $wordFilter->clearCache();
            } catch (PDOException | Exception $e) {
                Db::rollback();
                return $this->error($e->getMessage());
            }
            
            if ($count) {
                return $this->success(__('Added %s words', [$count]));
            } else {
                return $this->error(__('No words were added, they may already exist'));
            }
        }
        
        return $this->success();
    }
    
    /**
     * 测试文本
     */
    public function testText(): Response
    {
        if ($this->request->isPost()) {
            $text = $this->request->post('text');
            if (!$text) {
                return $this->error(__('Text cannot be empty'));
            }
            
            $wordFilter = new WordFilterService();
            $result = $wordFilter->checkText($text);
            $filteredText = $wordFilter->filterText($text);
            
            return $this->success('', [
                'result' => $result,
                'filtered_text' => $filteredText,
                'has_blocked_words' => $result['contains'],
                'matched_words' => $result['matched_words']
            ]);
        }
        
        return $this->success();
    }

    /**
     * 批量导入屏蔽词
     */
    public function import(): Response
    {
        if ($this->request->isPost()) {
            $file = $this->request->file('file');
            if (!$file) {
                return $this->error(__('Please upload a file'));
            }
            
            $filePath = $file->getInfo('tmp_name');
            $content = file_get_contents($filePath);
            
            if (empty($content)) {
                return $this->error(__('File is empty'));
            }
            
            // 按行分割
            $words = explode("\n", str_replace("\r", "", $content));
            $words = array_filter(array_map('trim', $words));
            
            if (empty($words)) {
                return $this->error(__('No valid words found'));
            }
            
            $category = $this->request->post('category', '');
            $successCount = 0;
            $failCount = 0;
            
            Db::startTrans();
            try {
                foreach ($words as $word) {
                    // 跳过空行和注释行
                    if (empty($word) || $word[0] == '#') {
                        continue;
                    }
                    
                    // 检查是否已存在
                    $exists = $this->model->where('word', $word)->find();
                    if ($exists) {
                        $failCount++;
                        continue;
                    }
                    
                    $result = $this->model->insert([
                        'word' => $word,
                        'category' => $category,
                        'replacement' => '***',
                        'is_regex' => 0
                    ]);
                    
                    if ($result) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                }
                
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                return $this->error($e->getMessage());
            }
            
            return $this->success(__('Imported successfully: %d, Failed: %d', [$successCount, $failCount]));
        }
        
        return $this->success();
    }
    
    /**
     * 清除缓存
     */
    public function clearCache(): Response
    {
        if ($this->request->isPost()) {
            $service = new WordFilterService();
            $service->clearCache();
            return $this->success(__('Cache cleared successfully'));
        }
        
        return $this->error(__('Invalid request'));
    }
} 