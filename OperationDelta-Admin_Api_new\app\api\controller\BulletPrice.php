<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\controller\Frontend;
use app\api\service\CacheManager;
use think\facade\Db;
use think\Response;
use think\App;

class BulletPrice extends Frontend
{
    /**
     * 无需登录的方法
     * @var array
     */
    protected array $noNeedLogin = ['getBulletTypes', 'getPredictList', 'updatePredict'];
    
    private CacheManager $cacheManager;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cacheManager = new CacheManager();
    }

    /**
     * 获取子弹类型列表
     * @return Response
     */
    public function getBulletTypes()
    {
        try {
            // 构建缓存键
            $cacheKey = 'bullet:types';
            
            // 使用缓存管理器获取数据
            $types = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_STATIC_DATA, function() {
                return $this->fetchBulletTypesData();
            });

            return json([
                'code' => 1,
                'message' => '获取成功',
                'data' => $types
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取子弹类型数据
     */
    private function fetchBulletTypesData(): array
    {
        // 获取子弹类型基本信息
        $types = Db::name('sjz_item_categories')
            ->where('primary_class', 'ammo')
            ->where('delete_time', null)
            ->field([
                'id',
                'second_class as code',
                'second_class_cn as name',
                'item_count'
            ])
            ->order('item_count', 'desc')
            ->select()
            ->toArray();

        // 为每个子弹类型添加图片
        foreach ($types as &$type) {
            // 查询该类型下的子弹，获取图片
            $bulletItem = Db::name('sjz_items')
                ->alias('i')
                ->where('i.category_id', $type['id'])
                ->where('i.delete_time', null)
                ->field([
                    'i.object_id',
                    'i.pic as image_url'
                ])
                ->order('i.avg_price', 'desc') // 按平均价格排序，获取价值较高的子弹图片
                ->limit(1)
                ->find();
            
            // 如果找到了子弹，使用其图片
            if ($bulletItem && !empty($bulletItem['image_url'])) {
                $type['image_url'] = $bulletItem['image_url'];
            } else {
                // 如果没有找到，或图片为空，使用默认图片
                $type['image_url'] = '#';
            }
        }

        return $types;
    }

    /**
     * 获取子弹价格预测列表
     * @return Response
     */
    public function getPredictList()
    {
        try {
            // 缓存key根据参数生成
            $limit = $this->request->param('limit/d', 5);
            $typeId = $this->request->param('type_id/d', 0);
            
            // 构建缓存键
            $cacheKey = 'bullet:predict:list:' . md5(json_encode([
                'limit' => $limit,
                'type_id' => $typeId
            ]));
            
            // 使用缓存管理器获取数据
            $bulletList = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($limit, $typeId) {
                return $this->fetchPredictListData($limit, $typeId);
            });

            return json([
                'code' => 1,
                'message' => '获取成功',
                'data' => $bulletList
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取子弹价格预测数据
     */
    private function fetchPredictListData(int $limit, int $typeId): array
    {
        try {
            // 构建查询
            $query = Db::name('sjz_items')
                ->alias('i')
                ->join('sjz_item_categories c', 'i.category_id = c.id')
                ->leftJoin('sjz_latest_prices lp', 'i.object_id = lp.object_id')
                ->where('c.primary_class', 'ammo')
                ->where('i.delete_time', null);

            // 如果指定了类型，则按类型筛选
            if ($typeId > 0) {
                $query->where('c.id', $typeId);
            }

            // 获取子弹列表
            $bulletList = $query->field([
                    'i.object_id as id',
                    'i.object_name as name',
                    'i.pic as image_url',
                    'i.grade as grade',
                    'c.second_class_cn as bullet_type',
                    'c.second_class as bullet_code',
                    'COALESCE(lp.current_price, 0) as current_price',
                    'COALESCE(lp.last_update_timestamp, NOW()) as update_time'
                ])
                ->order('lp.current_price', 'desc')
                ->limit($limit)
                ->select()
                ->toArray();

            // 如果没有数据，返回空数组
            if (empty($bulletList)) {
                return [];
            }

            // 批量查询优化开始
            $bulletIds = array_column($bulletList, 'id');
            // 1. 批量查所有弹药的90天价格历史
            $allPriceHistory = Db::name('sjz_price_history')
                ->whereIn('object_id', $bulletIds)
                ->where('timestamp', '>=', date('Y-m-d H:i:s', strtotime('-90 days')))
                ->order('timestamp', 'desc')
                ->select()
                ->toArray();
            $priceHistoryMap = [];
            foreach ($allPriceHistory as $row) {
                $priceHistoryMap[$row['object_id']][] = $row;
            }
            // 2. 批量查所有弹药的相关枪械
            $allGunAmmoRelations = Db::name('sjz_gun_ammo_relation')
                ->whereIn('ammo_type', $bulletIds)
                ->select()
                ->toArray();
            $gunRelationMap = [];
            foreach ($allGunAmmoRelations as $row) {
                $gunRelationMap[$row['ammo_type']][] = $row['object_id'];
            }
            // 3. 批量查所有相关枪械的配件
            $allRelatedGunIds = array_unique(array_merge(...array_values($gunRelationMap)));
            $allAccessorySlots = [];
            if (!empty($allRelatedGunIds)) {
                $allAccessorySlots = Db::name('sjz_gun_accessory_slots')
                    ->whereIn('object_id', $allRelatedGunIds)
                    ->select()
                    ->toArray();
            }
            $accessoryMap = [];
            foreach ($allAccessorySlots as $row) {
                $accessoryMap[$row['object_id']][] = $row['object_id'];
            }
            // 4. 批量查所有相关枪械的原材料
            $allCollectibles = [];
            if (!empty($allRelatedGunIds)) {
                $allCollectibles = Db::name('sjz_collectibles')
                    ->whereIn('object_id', $allRelatedGunIds)
                    ->select()
                    ->toArray();
            }
            $materialMap = [];
            foreach ($allCollectibles as $row) {
                if ($row['materials']) {
                    $pairs = explode(',', $row['materials']);
                    foreach ($pairs as $pair) {
                        $parts = explode(':', $pair);
                        if (count($parts) > 0) {
                            $materialMap[$row['object_id']][] = intval($parts[0]);
                        }
                    }
                }
            }
            // 5. 批量查所有相关ID的90天价格历史（配件、原材料）
            $allAccessoryIds = array_unique(array_merge(...array_values($accessoryMap)));
            $allMaterialIds = array_unique(array_merge(...array_values($materialMap)));
            $allRelatedIds = array_unique(array_merge($allRelatedGunIds, $allAccessoryIds, $allMaterialIds));
            $allRelatedPriceHistory = [];
            if (!empty($allRelatedIds)) {
                $allRelatedPriceHistory = Db::name('sjz_price_history')
                    ->whereIn('object_id', $allRelatedIds)
                    ->where('timestamp', '>=', date('Y-m-d H:i:s', strtotime('-90 days')))
                    ->select()
                    ->toArray();
            }
            $relatedPriceMap = [];
            foreach ($allRelatedPriceHistory as $row) {
                $relatedPriceMap[$row['object_id']][] = $row;
            }
            // 批量查询优化结束

            // 相关品类均价优化
            $getAvgPrice = function($objectIds) use ($relatedPriceMap) {
                if (empty($objectIds)) return 0;
                $sum = 0; $count = 0;
                foreach ($objectIds as $oid) {
                    if (!empty($relatedPriceMap[$oid])) {
                        $avg = array_sum(array_column($relatedPriceMap[$oid], 'price')) / count($relatedPriceMap[$oid]);
                        $sum += $avg;
                        $count++;
                    }
                }
                return $count > 0 ? round($sum / $count, 2) : 0;
            };
            $getChangePercent = function($objectIds) use ($relatedPriceMap) {
                if (empty($objectIds)) return 0;
                $now = time();
                $old = $now - (15 * 86400);
                $sumNow = 0; $sumOld = 0; $count = 0;
                foreach ($objectIds as $oid) {
                    if (!empty($relatedPriceMap[$oid])) {
                        $nowArr = array_filter($relatedPriceMap[$oid], function($row) use ($old) {
                            return strtotime($row['timestamp']) >= $old;
                        });
                        $oldArr = array_filter($relatedPriceMap[$oid], function($row) use ($old) {
                            return strtotime($row['timestamp']) < $old && strtotime($row['timestamp']) >= ($old - 15 * 86400);
                        });
                        $nowAvg = !empty($nowArr) ? array_sum(array_column($nowArr, 'price')) / count($nowArr) : 0;
                        $oldAvg = !empty($oldArr) ? array_sum(array_column($oldArr, 'price')) / count($oldArr) : 0;
                        if ($nowAvg && $oldAvg) {
                            $sumNow += $nowAvg;
                            $sumOld += $oldAvg;
                            $count++;
                        }
                    }
                }
                if ($count > 0 && $sumOld > 0) {
                    return round(($sumNow - $sumOld) / $sumOld * 100, 2);
                }
                return 0;
            };

            // 计算预测价格和详细信息
            foreach ($bulletList as &$bullet) {
                // 用批量查到的价格历史
                $priceHistory = $priceHistoryMap[$bullet['id']] ?? [];

                // ========== 周周期分析 ===========
                $weeklyPattern = [
                    'mon' => 0, 'tue' => 0, 'wed' => 0, 'thu' => 0, 'fri' => 0, 'sat' => 0, 'sun' => 0
                ];
                $weekCount = [
                    'mon' => 0, 'tue' => 0, 'wed' => 0, 'thu' => 0, 'fri' => 0, 'sat' => 0, 'sun' => 0
                ];
                foreach ($priceHistory as $record) {
                    $w = strtolower(date('D', strtotime($record['timestamp'])));
                    if ($w === 'mon') $weeklyPattern['mon'] += $record['price'];
                    if ($w === 'tue') $weeklyPattern['tue'] += $record['price'];
                    if ($w === 'wed') $weeklyPattern['wed'] += $record['price'];
                    if ($w === 'thu') $weeklyPattern['thu'] += $record['price'];
                    if ($w === 'fri') $weeklyPattern['fri'] += $record['price'];
                    if ($w === 'sat') $weeklyPattern['sat'] += $record['price'];
                    if ($w === 'sun') $weeklyPattern['sun'] += $record['price'];
                    $weekCount[$w]++;
                }
                foreach ($weeklyPattern as $k => $v) {
                    $weeklyPattern[$k] = $weekCount[$k] > 0 ? round($v / $weekCount[$k], 2) : 0;
                }
                $bullet['weekly_pattern'] = $weeklyPattern;

                // ========== 联动分析 ===========
                // 1. 相关枪械
                $relatedGunIds = $gunRelationMap[$bullet['id']] ?? [];
                $relatedAccessoryIds = [];
                foreach ($relatedGunIds as $gid) {
                    if (isset($accessoryMap[$gid])) {
                        $relatedAccessoryIds = array_merge($relatedAccessoryIds, $accessoryMap[$gid]);
                    }
                }
                $relatedAccessoryIds = array_unique($relatedAccessoryIds);
                $relatedMaterialIds = [];
                foreach ($relatedGunIds as $gid) {
                    if (isset($materialMap[$gid])) {
                        $relatedMaterialIds = array_merge($relatedMaterialIds, $materialMap[$gid]);
                    }
                }
                $relatedMaterialIds = array_unique($relatedMaterialIds);

                // 2. 相关品类价格趋势
                $bullet['related_items_trend'] = [
                    'gun_avg_price' => $getAvgPrice($relatedGunIds),
                    'gun_change_percent' => $getChangePercent($relatedGunIds),
                    'accessory_avg_price' => $getAvgPrice($relatedAccessoryIds),
                    'accessory_change_percent' => $getChangePercent($relatedAccessoryIds),
                    'material_avg_price' => $getAvgPrice($relatedMaterialIds),
                    'material_change_percent' => $getChangePercent($relatedMaterialIds)
                ];
                $bullet['linkage_direction'] = $this->calculateLinkageDirection($bullet['related_items_trend']);
                $bullet['linkage_strength'] = $this->calculateLinkageStrength($relatedGunIds, $relatedAccessoryIds, $relatedMaterialIds, $relatedPriceMap);

                // 确保价格历史数据存在且有效
                if (!empty($priceHistory) && count($priceHistory) >= 3) {
                    // 预处理数据 - 转换时间戳和数值类型
                    $prices = [];
                    $timestamps = [];
                    $dayPrices = []; // 按天分组的价格
                    $weeklyData = []; // 按周分组的数据
                    $monthlyData = []; // 按月分组的数据
                    
                    // 初始化过去30天、60天和90天的价格指标
                    $last30DaysAvg = 0;
                    $last60DaysAvg = 0;
                    $last90DaysAvg = 0;
                    $maxPrice = 0;
                    $minPrice = PHP_INT_MAX;
                    
                    // 处理原始数据
                    foreach ($priceHistory as $record) {
                        $price = floatval($record['price']);
                        $timestamp = strtotime($record['timestamp']);
                        $date = date('Y-m-d', $timestamp);
                        $week = date('Y-W', $timestamp);
                        $month = date('Y-m', $timestamp);
                        
                        // 追加数据
                        $prices[] = $price;
                        $timestamps[] = $timestamp;
                        
                        // 更新最大和最小价格
                        $maxPrice = max($maxPrice, $price);
                        $minPrice = min($minPrice, $price);
                        
                        // 按天分组数据（取每天的平均价格）
                        if (!isset($dayPrices[$date])) {
                            $dayPrices[$date] = ['sum' => 0, 'count' => 0];
                        }
                        $dayPrices[$date]['sum'] += $price;
                        $dayPrices[$date]['count'] += 1;
                        
                        // 按周分组数据
                        if (!isset($weeklyData[$week])) {
                            $weeklyData[$week] = ['sum' => 0, 'count' => 0];
                        }
                        $weeklyData[$week]['sum'] += $price;
                        $weeklyData[$week]['count'] += 1;
                        
                        // 按月分组数据
                        if (!isset($monthlyData[$month])) {
                            $monthlyData[$month] = ['sum' => 0, 'count' => 0];
                        }
                        $monthlyData[$month]['sum'] += $price;
                        $monthlyData[$month]['count'] += 1;
                    }
                    
                    // 计算不同时间段的平均价格
                    $now = time();
                    $days30Ago = $now - (30 * 86400);
                    $days60Ago = $now - (60 * 86400);
                    
                    $prices30 = [];
                    $prices60 = [];
                    $prices90 = $prices; // 全部90天的价格
                    
                    foreach ($priceHistory as $record) {
                        $timestamp = strtotime($record['timestamp']);
                        $price = floatval($record['price']);
                        
                        if ($timestamp >= $days30Ago) {
                            $prices30[] = $price;
                        }
                        
                        if ($timestamp >= $days60Ago) {
                            $prices60[] = $price;
                        }
                    }
                    
                    // 计算不同时间段的平均价格
                    $last30DaysAvg = !empty($prices30) ? array_sum($prices30) / count($prices30) : 0;
                    $last60DaysAvg = !empty($prices60) ? array_sum($prices60) / count($prices60) : 0;
                    $last90DaysAvg = !empty($prices90) ? array_sum($prices90) / count($prices90) : 0;
                    
                    // 计算每天的平均价格数组
                    $dailyAvgPrices = [];
                    $dailyTimestamps = [];
                    
                    foreach ($dayPrices as $date => $data) {
                        $dailyAvgPrices[] = $data['sum'] / $data['count'];
                        $dailyTimestamps[] = strtotime($date);
                    }
                    
                    // 对数据进行排序（按时间从早到晚）
                    array_multisort($dailyTimestamps, SORT_ASC, $dailyAvgPrices);
                    
                    // 计算价格波动性 - 标准差和变异系数
                    $priceStdDev = $this->calculateStandardDeviation($prices);
                    $priceCV = $last90DaysAvg > 0 ? ($priceStdDev / $last90DaysAvg) * 100 : 0;
                    
                    // 计算价格变化率
                    $priceChangeRates = [];
                    for ($i = 1; $i < count($dailyAvgPrices); $i++) {
                        if ($dailyAvgPrices[$i-1] > 0) {
                            $priceChangeRates[] = ($dailyAvgPrices[$i] - $dailyAvgPrices[$i-1]) / $dailyAvgPrices[$i-1] * 100;
                        }
                    }
                    
                    // 计算平均变化率
                    $avgChangeRate = !empty($priceChangeRates) ? array_sum($priceChangeRates) / count($priceChangeRates) : 0;
                    
                    // 价格趋势计算 - 线性回归
                    list($slope, $intercept) = $this->calculateLinearRegression($dailyTimestamps, $dailyAvgPrices);
                    
                    // 价格趋势计算 - 指数平滑
                    $alpha = 0.3; // 平滑因子
                    $exponentialForecast = $this->calculateExponentialSmoothing($dailyAvgPrices, $alpha);
                    
                    // 价格趋势计算 - 移动平均线
                    $maWindow = min(7, count($dailyAvgPrices));
                    $movingAvgForecast = $this->calculateMovingAverage($dailyAvgPrices, $maWindow);
                    
                    // 计算趋势可靠性
                    $trendReliability = $this->calculateTrendReliability($slope, $priceCV, count($priceHistory));
                    
                    // 市场周期性分析
                    $seasonality = $this->detectSeasonality($dailyAvgPrices);
                    
                    // 价格压力和支撑位
                    $priceLevels = $this->calculatePriceLevels($prices, $dailyAvgPrices);
                    
                    // 预测价格 - 融合多种算法结果
                    $linearPrediction = end($dailyAvgPrices) + $slope * 86400; // 线性回归
                    $expPrediction = end($exponentialForecast);
                    $maPrediction = end($movingAvgForecast);
                    
                    // 考虑价格趋势可靠性的加权预测
                    $weights = [
                        'linear' => $trendReliability['overall'] * 0.5,
                        'exp' => $trendReliability['overall'] * 0.3,
                        'ma' => 0.2
                    ];
                    
                    $predictedPrice = ($linearPrediction * $weights['linear'] + 
                                       $expPrediction * $weights['exp'] + 
                                       $maPrediction * $weights['ma']) / 
                                      array_sum($weights);
                    
                    // 趋势强度和方向判断
                    $trendStrength = abs($slope) > 0.1 ? 'strong' : 'weak';
                    $trendDirection = $predictedPrice > $bullet['current_price'] ? 'up' : 
                                   ($predictedPrice < $bullet['current_price'] ? 'down' : 'stable');
                    
                    // 预测可信度评估
                    $confidenceScore = $this->calculateConfidenceScore(
                        count($priceHistory), 
                        $trendReliability['overall'], 
                        $priceCV
                    );
                    
                    // 增加图表数据点数量，使用最近15天的价格历史，用于图表显示
                    $displayHistory = array_slice($priceHistory, 0, 15);
                    
                    // 添加分析结果到子弹数据
                    $bullet['predict_price'] = round($predictedPrice);
                    $bullet['price_volatility'] = round($priceCV, 2);
                    $bullet['trend_strength'] = $trendStrength;
                    $bullet['trend'] = $trendDirection;
                    $bullet['confidence'] = $confidenceScore;
                    $bullet['price_history'] = array_map(function($record) {
                        return [
                            'price' => $record['price'],
                            'timestamp' => $record['timestamp']
                        ];
                    }, $displayHistory);
                    
                    // 预测未来7天、14天和30天的价格
                    $futurePredictions = [];
                    $dailyChangeRate = $slope * 86400 / $bullet['current_price'] * 100; // 每日变化率（百分比）
                    
                    // 预测未来不同时间点的价格
                    $intervals = [1, 3, 7, 14, 30]; // 预测间隔（天）
                    foreach ($intervals as $days) {
                        $futurePrice = round($predictedPrice + $slope * 86400 * ($days - 1));
                        $futurePredictions[$days] = [
                            'days' => $days,
                            'price' => $futurePrice,
                            'change_percent' => round(($futurePrice - $bullet['current_price']) / $bullet['current_price'] * 100, 2),
                            'date' => date('Y-m-d', strtotime("+{$days} days"))
                        ];
                    }
                    
                    // 计算价格达到特定目标的预计时间
                    $targetPrices = [];
                    
                    // 如果有明确的价格趋势
                    if (abs($slope) > 0.000001) {
                        // 计算达到支撑位和压力位的时间
                        $daysToSupport = $slope < 0 ? round(($priceLevels['support'] - $bullet['current_price']) / ($slope * 86400)) : null;
                        $daysToResistance = $slope > 0 ? round(($priceLevels['resistance'] - $bullet['current_price']) / ($slope * 86400)) : null;
                        
                        // 添加关键价格目标
                        $priceTargets = [];
                        
                        // 向上目标价格（如果是上涨趋势）
                        if ($slope > 0) {
                            $priceTargets[] = $bullet['current_price'] * 1.1; // 10%涨幅
                            $priceTargets[] = $bullet['current_price'] * 1.25; // 25%涨幅
                            $priceTargets[] = $bullet['current_price'] * 1.5; // 50%涨幅
                            $priceTargets[] = $maxPrice * 1.1; // 历史最高价的110%
                        } 
                        // 向下目标价格（如果是下跌趋势）
                        else if ($slope < 0) {
                            $priceTargets[] = $bullet['current_price'] * 0.9; // 10%跌幅
                            $priceTargets[] = $bullet['current_price'] * 0.75; // 25%跌幅
                            $priceTargets[] = $bullet['current_price'] * 0.5; // 50%跌幅
                            $priceTargets[] = $minPrice * 0.9; // 历史最低价的90%
                        }
                        
                        // 计算达到每个目标价格的天数
                        foreach ($priceTargets as $targetPrice) {
                            $daysToTarget = round(($targetPrice - $bullet['current_price']) / ($slope * 86400));
                            
                            // 只添加有意义的正向预测（上涨趋势对应上涨目标，下跌趋势对应下跌目标）
                            if ($daysToTarget > 0 && $daysToTarget <= 90) { // 只预测未来90天内
                                $targetDate = date('Y-m-d', strtotime("+{$daysToTarget} days"));
                                $targetPrices[] = [
                                    'price' => round($targetPrice),
                                    'days' => $daysToTarget,
                                    'date' => $targetDate,
                                    'change_percent' => round(($targetPrice - $bullet['current_price']) / $bullet['current_price'] * 100, 2)
                                ];
                            }
                        }
                    }
                    
                    // 添加额外的分析数据
                    $bullet['analysis'] = [
                        'max_price' => $maxPrice,
                        'min_price' => $minPrice,
                        'price_range' => $maxPrice - $minPrice,
                        'last_30_days_avg' => round($last30DaysAvg, 2),
                        'last_60_days_avg' => round($last60DaysAvg, 2),
                        'last_90_days_avg' => round($last90DaysAvg, 2),
                        'avg_change_rate' => round($avgChangeRate, 2),
                        'support_level' => round($priceLevels['support'], 2),
                        'resistance_level' => round($priceLevels['resistance'], 2),
                        'seasonality' => $seasonality,
                        'trend_reliability' => $trendReliability,
                        'daily_change_rate' => round($dailyChangeRate, 2),
                        'future_predictions' => $futurePredictions,
                        'target_prices' => $targetPrices
                    ];
                } else {
                    // 如果没有足够的历史数据
                    $bullet['predict_price'] = $bullet['current_price'];
                    $bullet['price_volatility'] = 0;
                    $bullet['trend_strength'] = 'unknown';
                    $bullet['trend'] = 'stable';
                    $bullet['confidence'] = 'low';
                    $bullet['price_history'] = [];
                    $bullet['analysis'] = [
                        'max_price' => $bullet['current_price'],
                        'min_price' => $bullet['current_price'],
                        'price_range' => 0,
                        'last_30_days_avg' => $bullet['current_price'],
                        'last_60_days_avg' => $bullet['current_price'],
                        'last_90_days_avg' => $bullet['current_price'],
                        'avg_change_rate' => 0,
                        'support_level' => $bullet['current_price'],
                        'resistance_level' => $bullet['current_price'],
                        'seasonality' => 'unknown',
                        'trend_reliability' => [
                            'data_points' => 0,
                            'stability' => 0,
                            'consistency' => 0,
                            'overall' => 0
                        ],
                        'daily_change_rate' => 0,
                        'future_predictions' => [
                            1 => [
                                'days' => 1,
                                'price' => $bullet['current_price'],
                                'change_percent' => 0,
                                'date' => date('Y-m-d', strtotime("+1 days"))
                            ],
                            3 => [
                                'days' => 3,
                                'price' => $bullet['current_price'],
                                'change_percent' => 0,
                                'date' => date('Y-m-d', strtotime("+3 days"))
                            ],
                            7 => [
                                'days' => 7,
                                'price' => $bullet['current_price'],
                                'change_percent' => 0,
                                'date' => date('Y-m-d', strtotime("+7 days"))
                            ],
                            14 => [
                                'days' => 14,
                                'price' => $bullet['current_price'],
                                'change_percent' => 0,
                                'date' => date('Y-m-d', strtotime("+14 days"))
                            ],
                            30 => [
                                'days' => 30,
                                'price' => $bullet['current_price'],
                                'change_percent' => 0,
                                'date' => date('Y-m-d', strtotime("+30 days"))
                            ]
                        ],
                        'target_prices' => []
                    ];
                }
            }

            return $bulletList;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 更新子弹价格预测
     * @return Response
     */
    public function updatePredict()
    {
        try {
            // 直接调用获取列表方法
            return $this->getPredictList();
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 计算标准差
     * @param array $values 数值数组
     * @return float 标准差
     */
    private function calculateStandardDeviation(array $values): float
    {
        $count = count($values);
        if ($count < 2) {
            return 0;
        }
        
        $mean = array_sum($values) / $count;
        $variance = 0.0;
        
        foreach ($values as $value) {
            $variance += pow($value - $mean, 2);
        }
        
        return sqrt($variance / $count);
    }
    
    /**
     * 计算线性回归（最小二乘法）
     * @param array $x X值数组
     * @param array $y Y值数组
     * @return array [$slope, $intercept] 斜率和截距
     */
    private function calculateLinearRegression(array $x, array $y): array
    {
        $n = count($x);
        if ($n != count($y) || $n < 2) {
            return [0, 0];
        }
        
        $sumX = array_sum($x);
        $sumY = array_sum($y);
        $sumXY = 0;
        $sumXX = 0;
        
        for ($i = 0; $i < $n; $i++) {
            $sumXY += ($x[$i] * $y[$i]);
            $sumXX += ($x[$i] * $x[$i]);
        }
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumXX - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;
        
        return [$slope, $intercept];
    }
    
    /**
     * 计算指数平滑预测
     * @param array $values 时间序列数据
     * @param float $alpha 平滑因子(0-1)
     * @return array 平滑后的数据
     */
    private function calculateExponentialSmoothing(array $values, float $alpha = 0.3): array
    {
        $result = [];
        $n = count($values);
        
        if ($n < 1) {
            return $result;
        }
        
        $result[0] = $values[0];
        
        for ($i = 1; $i < $n; $i++) {
            $result[$i] = $alpha * $values[$i] + (1 - $alpha) * $result[$i - 1];
        }
        
        // 预测下一个值
        $result[] = $alpha * $values[$n - 1] + (1 - $alpha) * $result[$n - 1];
        
        return $result;
    }
    
    /**
     * 计算移动平均
     * @param array $values 时间序列数据
     * @param int $window 窗口大小
     * @return array 移动平均结果
     */
    private function calculateMovingAverage(array $values, int $window): array
    {
        $result = [];
        $n = count($values);
        
        if ($n < $window) {
            return $values;
        }
        
        // 初始窗口之前的值保持不变
        for ($i = 0; $i < $window - 1; $i++) {
            $result[$i] = $values[$i];
        }
        
        // 计算移动平均
        for ($i = $window - 1; $i < $n; $i++) {
            $sum = 0;
            for ($j = 0; $j < $window; $j++) {
                $sum += $values[$i - $j];
            }
            $result[$i] = $sum / $window;
        }
        
        // 预测下一个值 - 使用最后窗口的平均值
        $lastWindowSum = 0;
        for ($i = $n - $window; $i < $n; $i++) {
            $lastWindowSum += $values[$i];
        }
        $result[] = $lastWindowSum / $window;
        
        return $result;
    }
    
    /**
     * 检测时间序列数据的季节性
     * @param array $values 时间序列数据
     * @return string 季节性描述
     */
    private function detectSeasonality(array $values): string
    {
        $n = count($values);
        
        if ($n < 14) { // 至少需要两周的数据
            return 'insufficient_data';
        }
        
        // 简单的自相关分析
        $maxLag = min(floor($n / 4), 7); // 最大滞后期
        $correlations = [];
        
        for ($lag = 1; $lag <= $maxLag; $lag++) {
            $sum = 0;
            $count = 0;
            
            for ($i = 0; $i < $n - $lag; $i++) {
                $sum += ($values[$i] * $values[$i + $lag]);
                $count++;
            }
            
            $correlations[$lag] = $count > 0 ? $sum / $count : 0;
        }
        
        // 寻找相关性峰值
        arsort($correlations);
        $topLag = key($correlations);
        
        // 根据滞后期判断季节性
        if ($correlations[$topLag] > 0.7) {
            if ($topLag == 1) {
                return 'daily';
            } elseif ($topLag == 7) {
                return 'weekly';
            } else {
                return 'periodic_' . $topLag;
            }
        }
        
        return 'non_seasonal';
    }
    
    /**
     * 计算价格支撑位和压力位
     * @param array $allPrices 所有价格点
     * @param array $dailyAvgPrices 每日平均价格
     * @return array ['support' => 支撑位, 'resistance' => 压力位]
     */
    private function calculatePriceLevels(array $allPrices, array $dailyAvgPrices): array
    {
        // 如果没有足够数据，则返回当前价格
        if (empty($allPrices) || empty($dailyAvgPrices)) {
            return ['support' => 0, 'resistance' => 0];
        }
        
        $currentPrice = end($dailyAvgPrices);
        
        // 将价格排序
        $sortedPrices = $allPrices;
        sort($sortedPrices);
        
        // 找到当前价格的位置
        $position = 0;
        foreach ($sortedPrices as $i => $price) {
            if ($price >= $currentPrice) {
                $position = $i;
                break;
            }
        }
        
        // 计算支撑位 - 当前价格下方最近的价格集中区
        $lowerPrices = array_slice($sortedPrices, 0, $position);
        $support = !empty($lowerPrices) ? end($lowerPrices) : $currentPrice * 0.9;
        
        // 计算压力位 - 当前价格上方最近的价格集中区
        $upperPrices = array_slice($sortedPrices, $position);
        $resistance = !empty($upperPrices) && count($upperPrices) > 1 ? $upperPrices[1] : $currentPrice * 1.1;
        
        return ['support' => $support, 'resistance' => $resistance];
    }
    
    /**
     * 计算趋势可靠性
     * @param float $slope 线性回归斜率
     * @param float $volatility 价格波动性
     * @param int $dataPoints 数据点数量
     * @return array 可靠性指标
     */
    private function calculateTrendReliability(float $slope, float $volatility, int $dataPoints): array
    {
        // 数据点分数 (0-1)
        $dataPointsScore = min(1, $dataPoints / 30);
        
        // 稳定性分数 (波动性越低越好)
        $stabilityScore = max(0, 1 - min(1, $volatility / 100));
        
        // 一致性分数 (斜率越大越好，表示趋势明显)
        $consistencyScore = min(1, abs($slope) * 10);
        
        // 综合评分
        $overallScore = ($dataPointsScore * 0.3) + ($stabilityScore * 0.4) + ($consistencyScore * 0.3);
        
        return [
            'data_points' => round($dataPointsScore, 2),
            'stability' => round($stabilityScore, 2),
            'consistency' => round($consistencyScore, 2),
            'overall' => round($overallScore, 2)
        ];
    }
    
    /**
     * 计算预测可信度
     * @param int $dataPoints 数据点数量
     * @param float $reliability 趋势可靠性
     * @param float $volatility 价格波动性
     * @return string 可信度级别 (high, medium, low)
     */
    private function calculateConfidenceScore(int $dataPoints, float $reliability, float $volatility): string
    {
        // 数据点权重
        $dataPointWeight = 0.3;
        // 可靠性权重
        $reliabilityWeight = 0.5;
        // 波动性权重 (波动性越低越好)
        $volatilityWeight = 0.2;
        
        // 标准化波动性 (0-1, 1表示波动性最低)
        $normalizedVolatility = max(0, 1 - min(1, $volatility / 100));
        
        // 数据点评分
        $dataPointScore = min(1, $dataPoints / 30);
        
        // 总体评分
        $score = ($dataPointScore * $dataPointWeight) + 
                 ($reliability * $reliabilityWeight) + 
                 ($normalizedVolatility * $volatilityWeight);
        
        // 确定可信度级别
        if ($score >= 0.7) {
            return 'high';
        } elseif ($score >= 0.4) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * 计算联动方向
     * @param array $trends 相关品类价格趋势
     * @return string 联动方向
     */
    private function calculateLinkageDirection(array $trends): string
    {
        $linkageDirection = '';
        if ($trends['gun_change_percent'] > 0 && $trends['accessory_change_percent'] > 0 && $trends['material_change_percent'] > 0) {
            $linkageDirection = 'up-up';
        } elseif ($trends['gun_change_percent'] < 0 && $trends['accessory_change_percent'] > 0 && $trends['material_change_percent'] > 0) {
            $linkageDirection = 'down-up';
        } elseif ($trends['gun_change_percent'] > 0 && ($trends['accessory_change_percent'] < 0 || $trends['material_change_percent'] < 0)) {
            $linkageDirection = 'up-down';
        } elseif ($trends['gun_change_percent'] < 0 && ($trends['accessory_change_percent'] < 0 || $trends['material_change_percent'] < 0)) {
            $linkageDirection = 'down-down';
        } else {
            $linkageDirection = 'mixed';
        }
        return $linkageDirection;
    }

    /**
     * 计算联动强度
     * @param array $gunIds 相关枪械ID
     * @param array $accessoryIds 相关配件ID
     * @param array $materialIds 相关原材料ID
     * @return float 联动强度
     */
    private function calculateLinkageStrength(array $gunIds, array $accessoryIds, array $materialIds, array $relatedPriceMap): float
    {
        $relatedIds = array_unique(array_merge($gunIds, $accessoryIds, $materialIds));
        $corScores = [];
        foreach ($relatedIds as $id) {
            $corScores[] = $this->calculateCorrelation($gunIds, $accessoryIds, $materialIds, $id, $relatedPriceMap);
        }
        return count($corScores) > 0 ? round(array_sum($corScores) / count($corScores), 2) : 0;
    }

    /**
     * 计算相关性
     * @param array $gunIds 相关枪械ID
     * @param array $accessoryIds 相关配件ID
     * @param array $materialIds 相关原材料ID
     * @param int $id 相关ID
     * @return float 相关性分数
     */
    private function calculateCorrelation(array $gunIds, array $accessoryIds, array $materialIds, int $id, array $relatedPriceMap): float
    {
        $arr1 = array_column($relatedPriceMap[$id] ?? [], 'price');
        $arr2 = array_column($relatedPriceMap[$id] ?? [], 'price');
        $n = min(count($arr1), count($arr2));
        if ($n < 3) return 0;
        $arr1 = array_slice($arr1, 0, $n);
        $arr2 = array_slice($arr2, 0, $n);
        $mean1 = array_sum($arr1) / $n;
        $mean2 = array_sum($arr2) / $n;
        $num = 0; $den1 = 0; $den2 = 0;
        for ($i = 0; $i < $n; $i++) {
            $num += ($arr1[$i] - $mean1) * ($arr2[$i] - $mean2);
            $den1 += pow($arr1[$i] - $mean1, 2);
            $den2 += pow($arr2[$i] - $mean2, 2);
        }
        if ($den1 == 0 || $den2 == 0) return 0;
        return round($num / sqrt($den1 * $den2), 2);
    }
} 