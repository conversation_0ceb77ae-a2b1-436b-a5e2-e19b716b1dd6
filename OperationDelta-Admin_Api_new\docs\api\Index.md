# Operation Delta API 文档

本文档提供了Operation Delta项目的API接口说明，包括请求参数、响应格式等详细信息。

## API 目录

### 前端相关接口

- [Index 接口](./Index.md)：前台和会员中心初始化接口，包括公告相关功能
- [Account 接口](./Account.md)：用户账户管理、资料修改、收藏等功能
- [Items 接口](./Items.md)：游戏内物品数据查询、价格历史和分类等功能
- [Ranking 接口](./Ranking.md)：价格排行榜相关功能，支持多种排序方式和筛选条件

### API 返回格式说明

所有API返回格式均为JSON，包含以下字段：

```json
{
  "code": 1,       // 状态码：1成功，0失败
  "msg": "成功",    // 提示信息
  "data": {        // 返回数据，失败时可能为null
    // 具体数据结构根据接口不同而变化
  }
}
```

### 常见状态码

| 状态码 | 描述 |
| ------ | ---- |
| 200 | 请求成功 |
| 400 | 参数错误 |
| 401 | 未授权（未登录） |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 认证方式

大部分接口需要认证才能访问，认证方式为Bearer Token：

```
Authorization: Bearer {token}
```

Token可以通过登录接口获取。部分公开接口无需认证即可访问。

### 分页参数

支持分页的接口通常使用以下参数：

| 参数名 | 类型 | 默认值 | 描述 |
| ------ | ---- | ------ | ---- |
| page | int | 1 | 页码，从1开始 |
| pageSize | int | 20 | 每页条数，默认20 |

分页返回的数据通常包含以下字段：

```json
{
  "total": 100,           // 总记录数
  "current": 1,           // 当前页码
  "pageSize": 20,         // 每页条数
  "pages": 5              // 总页数
}
``` 