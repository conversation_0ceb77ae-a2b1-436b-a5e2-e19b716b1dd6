<?php

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\model\sjz\FeatureRequests;
use app\common\model\sjz\FeatureVotes;
use app\api\service\WordFilterService;
use ba\Random;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Config;
use think\facade\Log;
use think\Response;

/**
 * 功能投票控制器
 */
class Feature extends Frontend
{
    // 所有接口均不需要登录
    protected array $noNeedLogin = ['index', 'detail', 'submit', 'vote', 'getStatus'];
    
    /**
     * 构造函数
     */
    public function initialize(): void
    {
        parent::initialize();
        
        // 优先取 X-Client-ID，没有再取 client-id
        $clientId = $this->request->header('X-Client-ID') ?: $this->request->header('client-id');
        if (!$clientId) {
            $clientId = Random::uuid();
            response()->header(['X-Client-ID' => $clientId]);
        }
        $this->request->clientId = $clientId;
    }
    
    /**
     * 获取功能请求列表
     * @return Response
     * @throws DbException
     */
    public function index(): Response
    {
        $status = $this->request->param('status/a');  // 数组形式接收多个状态
        $search = $this->request->param('search/s', '');
        $sort = $this->request->param('sort/s', 'votes_desc');
        $page = $this->request->param('page/d', 1);
        $pageSize = $this->request->param('pageSize/d', 20);

        $model = new FeatureRequests();
        $query = $model->where('delete_time', null);

        // 所有用户只能看到已通过、开发中、已计划和已完成的功能请求
        $query->whereIn('status', ['approved', 'in_progress', 'planned', 'completed']);

        // 状态筛选
        if (!empty($status)) {
            $query->whereIn('status', $status);
        }

        // 关键词搜索
        if ($search !== '') {
            $query->where(function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                    ->whereOr('description', 'like', "%{$search}%");
            });
        }

        // 排序
        switch ($sort) {
            case 'votes_desc':
                $query->order('votes_count', 'desc');
                break;
            case 'votes_asc':
                $query->order('votes_count', 'asc');
                break;
            case 'newest':
                $query->order('create_time', 'desc');
                break;
            case 'oldest':
                $query->order('create_time', 'asc');
                break;
            default:
                $query->order('votes_count', 'desc');
        }

        // 二级排序
        $query->order('last_vote_time', 'desc');

        // 分页查询
        $total = $query->count();
        $list = $query->with(['user' => function ($query) {
            $query->field('id,username,nickname,avatar');
        }])->page($page, $pageSize)->select();

        // 添加是否已投票标志，并补充custom_name字段
        $clientId = $this->request->clientId;
        foreach ($list as &$item) {
            $item['voted'] = FeatureVotes::isVotedToday($item['id'], $clientId);
            // 显式补充custom_name字段，便于前端展示
            $item['custom_name'] = $item['custom_name'] ?? null;
        }

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'total' => $total,
                'list' => $list,
                // 返回当前用户今日已投票数
                'today_votes' => FeatureVotes::countTodayTotalVotes($clientId)
            ]
        ]);
    }
    
    /**
     * 获取功能请求详情
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function detail(): Response
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $feature = FeatureRequests::with(['user' => function ($query) {
            $query->field('id,username,nickname,avatar');
        }])->find($id);
        
        if (!$feature) {
            return json(['code' => 0, 'msg' => '功能请求不存在']);
        }
        
        // 检查是否已投票
        $clientId = $this->request->clientId;
        $feature['voted'] = FeatureVotes::isVotedToday($id, $clientId);
        
        // 添加状态文本
        $feature['status_text'] = FeatureRequests::STATUS_MAP[$feature['status']] ?? '未知';
        
        // 显式补充custom_name字段，便于前端展示
        $feature['custom_name'] = $feature['custom_name'] ?? null;
        
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $feature
        ]);
    }
    
    /**
     * 提交功能请求
     * @return Response
     */
    public function submit(): Response
    {
        // 检查是否允许匿名提交
        if (!Config::get('feature.allow_anonymous', true) && !$this->auth->isLogin()) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }
        
        $title = $this->request->post('title/s', '', 'trim');
        $description = $this->request->post('description/s', '', 'trim');
        // 新增：接收自定义名称
        $customName = $this->request->post('custom_name/s', '', 'trim');
        
        // 验证输入
        if (empty($title)) {
            return json(['code' => 0, 'msg' => '标题不能为空']);
        }
        
        if (empty($description)) {
            return json(['code' => 0, 'msg' => '描述不能为空']);
        }
        
        if (mb_strlen($title) > 100) {
            return json(['code' => 0, 'msg' => '标题长度不能超过100字符']);
        }
        
        if (mb_strlen($description) > 5000) {
            return json(['code' => 0, 'msg' => '描述长度不能超过5000字符']);
        }
        
        // 新增：未登录用户必须填写自定义名称
        if (!$this->auth->isLogin()) {
            if (empty($customName)) {
                return json(['code' => 0, 'msg' => '请填写您的名称']);
            }
            if (mb_strlen($customName) < 2 || mb_strlen($customName) > 20) {
                return json(['code' => 0, 'msg' => '名称长度需在2-20个字符之间']);
            }
        } else {
            // 已登录用户不保存 custom_name
            $customName = null;
        }
        
        // 检查屏蔽词
        $filter = new WordFilterService();
        $titleCheck = $filter->validateText($title);
        $descCheck = $filter->validateText($description);
        
        if (!$titleCheck['valid']) {
            return json([
                'code' => 0, 
                'msg' => '标题包含违禁词: ' . implode(', ', $titleCheck['matched_words'])
            ]);
        }
        
        if (!$descCheck['valid']) {
            return json([
                'code' => 0, 
                'msg' => '描述包含违禁词: ' . implode(', ', $descCheck['matched_words'])
            ]);
        }
        
        // 创建功能请求
        $feature = new FeatureRequests();
        $feature->title = htmlspecialchars($title); // 标题转义
        $feature->description = htmlspecialchars($description); // 描述转义
        $feature->client_id = $this->request->clientId;
        $feature->client_type = $this->request->header('X-Client-Type', 'web');
        $feature->ip_address = $this->request->ip();
        $feature->custom_name = $customName; // 新增：保存自定义名称
        
        // 设置用户ID（如果已登录）
        if ($this->auth->isLogin()) {
            $feature->user_id = $this->auth->id;
        }
        
        // 设置状态
        $feature->status = Config::get('feature.auto_approve', false) ? 'approved' : 'pending';

        // 调试：保存前记录数据
        Log::info('feature_submit', [
            'before_save' => $feature->toArray(),
            'custom_name' => $feature->custom_name,
            'time' => date('Y-m-d H:i:s'),
        ]);
        
        try {
            $saveResult = $feature->save();
            // 调试：保存后记录数据
            Log::info('feature_submit', [
                'after_save' => $feature->toArray(),
                'custom_name' => $feature->custom_name,
                'save_result' => $saveResult,
                'time' => date('Y-m-d H:i:s'),
            ]);
            
            return json([
                'code' => 1,
                'msg' => '提交成功，' . ($feature->status === 'pending' ? '请等待审核' : '感谢您的建议'),
                'data' => [
                    'id' => $feature->id,
                    'status' => $feature->status,
                    'custom_name' => $feature->custom_name // 新增：返回自定义名称
                ]
            ]);
        } catch (Exception $e) {
            // 调试：异常记录
            Log::error('feature_submit', [
                'exception' => $e->getMessage(),
                'time' => date('Y-m-d H:i:s'),
            ]);
            return json(['code' => 0, 'msg' => '提交失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 投票功能
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function vote(): Response
    {
        $id = $this->request->post('id/d');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        $feature = FeatureRequests::find($id);
        if (!$feature) {
            return json(['code' => 0, 'msg' => '功能请求不存在']);
        }
        
        // 检查功能状态
        if (!in_array($feature->status, ['approved', 'in_progress', 'planned'])) {
            return json(['code' => 0, 'msg' => '当前状态不允许投票']);
        }
        
        $clientId = $this->request->clientId;
        
        // 检查是否已投票
        if (FeatureVotes::isVotedToday($id, $clientId)) {
            return json(['code' => 0, 'msg' => '您今天已经对此功能投过票了']);
        }
        
        // 检查今日投票总数是否达到限制
        $maxVotesPerDay = Config::get('feature.max_votes_per_day', 10);
        $todayVotes = FeatureVotes::countTodayTotalVotes($clientId);
        
        if ($todayVotes >= $maxVotesPerDay) {
            return json(['code' => 0, 'msg' => "您今天的投票次数已达到上限({$maxVotesPerDay}次)"]);
        }
        
        // 创建投票记录
        $vote = new FeatureVotes();
        $vote->feature_id = $id;
        $vote->client_id = $clientId;
        $vote->client_type = $this->request->header('X-Client-Type', 'web');
        $vote->ip_address = $this->request->ip();
        $vote->vote_date = date('Y-m-d');
        
        // 如果已登录，记录用户ID
        if ($this->auth->isLogin()) {
            $vote->user_id = $this->auth->id;
        }
        
        try {
            // 开启事务
            $feature->startTrans();
            
            // 保存投票记录
            $vote->save();
            
            // 更新功能请求的投票数和最后投票时间
            $feature->votes_count = $feature->votes_count + 1;
            $feature->last_vote_time = date('Y-m-d H:i:s');
            $feature->save();
            
            // 提交事务
            $feature->commit();
            
            return json([
                'code' => 1,
                'msg' => '投票成功',
                'data' => [
                    'votes_count' => $feature->votes_count,
                    'today_votes' => $todayVotes + 1
                ]
            ]);
        } catch (Exception $e) {
            // 回滚事务
            $feature->rollback();
            return json(['code' => 0, 'msg' => '投票失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取功能状态统计
     * @return Response
     * @throws DbException
     */
    public function getStatus(): Response
    {
        $model = new FeatureRequests();
        $query = $model->where('delete_time', null);
        
        // 所有用户只能看到已通过、开发中、已计划和已完成的功能请求
        $query->whereIn('status', ['approved', 'in_progress', 'planned', 'completed']);
        
        // 统计各状态的数量
        $stats = [];
        $total = 0;
        
        foreach (FeatureRequests::STATUS_MAP as $status => $text) {
            $count = $model->where('status', $status)->count();
            
            // 跳过"待审核"和"已拒绝"状态，所有用户一致
            if (in_array($status, ['pending', 'rejected'])) {
                continue;
            }
            
            $stats[] = [
                'status' => $status,
                'text' => $text,
                'count' => $count
            ];
            
            $total += $count;
        }
        
        // 客户端ID
        $clientId = $this->request->clientId;
        
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'total' => $total,
                'stats' => $stats,
                'today_votes' => FeatureVotes::countTodayTotalVotes($clientId),
                'max_votes_per_day' => Config::get('feature.max_votes_per_day', 10)
            ]
        ]);
    }
} 