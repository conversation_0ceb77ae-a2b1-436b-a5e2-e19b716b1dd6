<?php

namespace app\api\controller;

use ba\Tree;
use Throwable;
use think\facade\Db;
use think\facade\Config;
use think\facade\Cache;
use app\common\controller\Frontend;
use app\common\library\token\TokenExpirationException;
use app\common\model\sjz\Announcements;
use app\api\service\CacheManager;
use think\Response;

class Index extends Frontend
{
    protected array $noNeedLogin = ['index', 'getAnnouncements', 'getAnnouncementDetail', 'getMapPasswords', 'getPriceChangeItemsList', 'getPriceChangeItems1', 'getBulletKeyCardRankings'];

    private CacheManager $cacheManager;

    public function initialize(): void
    {
        parent::initialize();
        $this->cacheManager = new CacheManager();
    }

    /**
     * 前台和会员中心的初始化请求
     * @throws Throwable
     */
    public function index(): void
    {
        $menus = [];
        if ($this->auth->isLogin()) {
            $rules     = [];
            $userMenus = $this->auth->getMenus();

            // 首页加载的规则，验权，但过滤掉会员中心菜单
            foreach ($userMenus as $item) {
                if ($item['type'] == 'menu_dir') {
                    $menus[] = $item;
                } elseif ($item['type'] != 'menu') {
                    $rules[] = $item;
                }
            }
            $rules = array_values($rules);
        } else {
            // 若是从前台会员中心内发出的请求，要求必须登录，否则会员中心异常
            $requiredLogin = $this->request->get('requiredLogin/b', false);
            if ($requiredLogin) {

                // 触发可能的 token 过期异常
                try {
                    $token = get_auth_token(['ba', 'user', 'token']);
                    $this->auth->init($token);
                } catch (TokenExpirationException) {
                    $this->error(__('Token expiration'), [], 409);
                }

                $this->error(__('Please login first'), [
                    'type' => $this->auth::NEED_LOGIN
                ], $this->auth::LOGIN_RESPONSE_CODE);
            }

            $rules = Db::name('user_rule')
                ->where('status', '1')
                ->where('no_login_valid', 1)
                ->where('type', 'in', ['route', 'nav', 'button'])
                ->order('weigh', 'desc')
                ->select()
                ->toArray();
            $rules = Tree::instance()->assembleChild($rules);
        }

        $this->success('', [
            'site'             => [
                'siteName'     => get_sys_config('site_name'),
                'recordNumber' => get_sys_config('record_number'),
                'version'      => get_sys_config('version'),
                'cdnUrl'       => full_url(),
                'bannerImgHaozi' => get_sys_config('banner_img_haozi'),
                'upload'       => keys_to_camel_case(get_upload_config(), ['max_size', 'save_name', 'allowed_suffixes', 'allowed_mime_types']),
            ],
            'openMemberCenter' => Config::get('buildadmin.open_member_center'),
            'userInfo'         => $this->auth->getUserInfo(),
            'rules'            => $rules,
            'menus'            => $menus,
        ]);
    }

    /**
     * 获取公告列表
     * @param string $type 公告类型
     * @param string $search 搜索关键词
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @param string $sort 排序方式 newest/oldest
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return Response
     */
    public function getAnnouncements()
    {
        $type = $this->request->param('type/a');  // 数组形式接收多个类型
        $search = $this->request->param('search/s', '');
        $startTime = $this->request->param('start_time/s', '');
        $endTime = $this->request->param('end_time/s', '');
        $sort = $this->request->param('sort/s', 'newest');
        $page = $this->request->param('page/d', 1);
        $pageSize = $this->request->param('pageSize/d', 20);

        // 构建缓存键，包含所有查询参数
        $cacheKey = 'announcements:list:' . md5(json_encode([
            'type' => $type,
            'search' => $search,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'sort' => $sort,
            'page' => $page,
            'pageSize' => $pageSize
        ]));

        // 使用 CacheManager 获取数据
        $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($type, $search, $startTime, $endTime, $sort, $page, $pageSize) {
            return $this->fetchAnnouncementsData($type, $search, $startTime, $endTime, $sort, $page, $pageSize);
        });

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $data
        ]);

    }

    /**
     * 获取公告数据
     */
    private function fetchAnnouncementsData($type, $search, $startTime, $endTime, $sort, $page, $pageSize)
    {
        $model = new Announcements();
        $query = $model->where('status', '1'); // 只查询已发布的公告

        // 类型筛选
        if (!empty($type)) {
            $query = $query->whereIn('type', $type);
        }

        // 关键词搜索
        if ($search !== '') {
            $query = $query->where(function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                    ->whereOr('brief', 'like', "%{$search}%")
                    ->whereOr('content', 'like', "%{$search}%");
            });
        }

        // 时间范围筛选
        if ($startTime && $endTime) {
            $query = $query->whereBetweenTime('publish_time', $startTime, $endTime);
        }

        // 排序
        $order = $sort === 'newest' ? 'publish_time DESC' : 'publish_time ASC';
        $query = $query->order($order);

        // 置顶公告优先
        $query = $query->order('is_top DESC');

        // 分页查询
        $total = $query->count();
        $list = $query->page($page, $pageSize)
            ->field('id, title, brief, type, is_top, publish_time, view_count,admin_name') // 只返回必要字段
            ->select();

        // 格式化数据
        $formattedList = [];
        foreach ($list as $item) {
            $itemArray = $item->toArray();
            $itemArray['time'] = strtotime($item->publish_time) * 1000; // 转换为毫秒级时间戳
            $formattedList[] = $itemArray;
        }

        return [
            'total' => $total,
            'list' => $formattedList
        ];

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }

    /**
     * 获取公告详情
     * @param int $id 公告ID
     * @return Response
     */
    public function getAnnouncementDetail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 无论是否有缓存，都更新浏览次数
        $model = new Announcements();
        $model->where('id', $id)->inc('view_count')->update();

        // 构建缓存键
        $cacheKey = 'announcement:detail:' . $id;

        // 使用 CacheManager 获取数据
        $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($id) {
            return $this->fetchAnnouncementDetail($id);
        });

        if (!$data) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        // 更新缓存中的浏览量（因为我们在缓存前已经更新了数据库）
        $data['view_count'] += 1;

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }

    /**
     * 获取公告详情数据
     */
    private function fetchAnnouncementDetail($id)
    {
        $model = new Announcements();
        $detail = $model->where('id', $id)
            ->where('status', '1')
            ->find();

        if (!$detail) {
            return null;
        }

        // 转换为数组并格式化时间
        $detailArray = $detail->toArray();
        $detailArray['time'] = strtotime($detail->publish_time) * 1000;

        return $detailArray;
    }



    /**
     * 获取子弹和钥匙卡排行榜数据
     *
     * @return Response
     */
    public function getBulletKeyCardRankings()
    {
        try {
            $limit = input('limit', 10, 'intval'); // 每个排行榜的数量限制

            // 构建缓存键
            $cacheKey = 'rankings:bullet_keycard_' . $limit;

            // 使用 CacheManager 获取数据
            $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($limit) {
                return $this->fetchBulletKeyCardRankings($limit);
            });

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            \think\facade\Log::error('获取子弹和钥匙卡排行榜失败: ' . $e->getMessage() . "\n堆栈：" . $e->getTraceAsString());
            return json([
                'code' => 0,
                'msg' => '获取排行榜数据失败: ' . $e->getMessage(),
                'data' => [
                    'bullet_rising' => [],
                    'bullet_falling' => [],
                    'keycard_rising' => [],
                    'keycard_falling' => []
                ]
            ]);
        }
    }

    /**
     * 获取子弹和钥匙卡排行榜数据
     */
    private function fetchBulletKeyCardRankings($limit)
    {
        // 使用精确分类匹配的查询：从ba_sjz_item_categories表获取precise category matching
        $optimizedSql = <<<SQL
SELECT
    i.object_id as id,
    i.object_id,
    i.object_name as name,
    i.pic as image_url,
    i.grade,
    lp.current_price,
    COALESCE(lp.price_24h_ago, lp.current_price) as previous_price,
    (lp.current_price - COALESCE(lp.price_24h_ago, lp.current_price)) as change_value,
    CASE
        WHEN COALESCE(lp.price_24h_ago, lp.current_price) > 0
        THEN ROUND(((lp.current_price - COALESCE(lp.price_24h_ago, lp.current_price)) / COALESCE(lp.price_24h_ago, lp.current_price)) * 100, 2)
        ELSE 0
    END as change_percentage,
    CASE
        WHEN lp.current_price > COALESCE(lp.price_24h_ago, lp.current_price) THEN 'up'
        WHEN lp.current_price < COALESCE(lp.price_24h_ago, lp.current_price) THEN 'down'
        ELSE 'stable'
    END as trend,
    CASE
        WHEN c.primary_class = 'ammo' THEN 'bullet'
        WHEN c.second_class = 'key' THEN 'keycard'
        ELSE 'other'
    END as item_type
FROM ba_sjz_items i
INNER JOIN ba_sjz_latest_prices lp ON i.object_id = lp.object_id
INNER JOIN ba_sjz_item_categories c ON i.category_id = c.id
WHERE (c.primary_class = 'ammo' OR c.second_class = 'key')
AND lp.current_price > 0
AND i.delete_time IS NULL
ORDER BY ABS(CASE
    WHEN COALESCE(lp.price_24h_ago, lp.current_price) > 0
    THEN ((lp.current_price - COALESCE(lp.price_24h_ago, lp.current_price)) / COALESCE(lp.price_24h_ago, lp.current_price)) * 100
    ELSE 0
END) DESC
LIMIT 200
SQL;

        // 执行优化后的查询
        $allData = Db::query($optimizedSql);

        // 分类处理数据
        $bulletRising = [];
        $bulletFalling = [];
        $keycardRising = [];
        $keycardFalling = [];

        // 处理查询结果
        foreach ($allData as $item) {
            $processedItem = [
                'id' => intval($item['id']),
                'object_id' => intval($item['object_id']),
                'name' => $item['name'],
                'image_url' => $item['image_url'] ?: '',
                'current_price' => floatval($item['current_price']),
                'previous_price' => floatval($item['previous_price']),
                'change_value' => floatval($item['change_value']),
                'change_percentage' => floatval($item['change_percentage']),
                'trend' => $item['trend'],
                'grade' => intval($item['grade'] ?: 0)
            ];

            // 根据物品类型和趋势分类
            if ($item['item_type'] === 'bullet') {
                if ($item['trend'] === 'up' && count($bulletRising) < $limit) {
                    $bulletRising[] = $processedItem;
                } elseif ($item['trend'] === 'down' && count($bulletFalling) < $limit) {
                    $bulletFalling[] = $processedItem;
                }
            } elseif ($item['item_type'] === 'keycard') {
                if ($item['trend'] === 'up' && count($keycardRising) < $limit) {
                    $keycardRising[] = $processedItem;
                } elseif ($item['trend'] === 'down' && count($keycardFalling) < $limit) {
                    $keycardFalling[] = $processedItem;
                }
            }
        }

        // 对下跌数据按跌幅从大到小排序（负值排序，跌幅大的在前）
        usort($bulletFalling, function($a, $b) {
            return $a['change_percentage'] <=> $b['change_percentage'];
        });

        usort($keycardFalling, function($a, $b) {
            return $a['change_percentage'] <=> $b['change_percentage'];
        });

        // 准备返回数据
        return [
            'bullet_rising' => $bulletRising,
            'bullet_falling' => $bulletFalling,
            'keycard_rising' => $keycardRising,
            'keycard_falling' => $keycardFalling
        ];
    }

    /**
     * 清除公告相关缓存
     * 
     * @param int|null $id 公告ID，如果为null则清除所有公告缓存
     */
    protected function clearAnnouncementCache($id = null)
    {
        if ($id) {
            // 清除特定公告的缓存
            Cache::store('redis')->delete('announcement:detail:' . $id);
        }
        
        // 清除公告列表相关的所有缓存
        Cache::store('redis')->tag('announcements')->clear();
    }
    
    /**
     * 清除统计数据缓存
     */
    protected function clearStatsCache()
    {
        Cache::store('redis')->delete('stats:dashboard_summary');
        Cache::store('redis')->delete('stats:price_change_items');
        Cache::store('redis')->delete('stats:bullet_price_index');
        // 可以选择性地保留总物品数缓存，因为它变化不频繁
        // Cache::store('redis')->delete('stats:total_items');
    }

    /**
     * 获取地图密码数据
     * 
     * @return Response
     */
    public function getMapPasswords()
    {
        try {
            // 构建缓存键
            $cacheKey = 'map_passwords:latest';

            // 使用 CacheManager 获取数据
            $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() {
                return $this->fetchMapPasswords();
            });

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            return json([
                'code' => 0,
                'msg' => '获取地图密码失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取地图密码数据
     */
    private function fetchMapPasswords()
    {
        // 直接从数据库获取最新的地图密码，包含json_data字段
        $passwords = Db::name('sjz_ditumima')
            ->field('daba, changgong, bakeshi, hangtian, chaoxijianyu, json_data')
            ->order('id DESC')
            ->limit(1)
            ->find();

        if (!$passwords) {
            // 无数据返回空对象
            return [
                'type' => 'empty',
                'title' => '每日彩蛋门密码',
                'update_time' => date('Y-m-d H:i:s'),
                'total_maps' => 0,
                'passwords' => [],
                'images' => []
            ];
        }

        // 优先使用JSON数据，如果不存在则使用原始字段
        if (!empty($passwords['json_data'])) {
            try {
                $jsonData = json_decode($passwords['json_data'], true);
                if ($jsonData && is_array($jsonData)) {
                    // 返回完整的JSON数据结构
                    return [
                        'type' => 'json',
                        'title' => $jsonData['title'] ?? '每日彩蛋门密码',
                        'update_time' => $jsonData['update_time'] ?? date('Y-m-d H:i:s'),
                        'total_maps' => $jsonData['total_maps'] ?? 0,
                        'passwords' => $jsonData['passwords'] ?? [],
                        'images' => $jsonData['images'] ?? []
                    ];
                } else {
                    throw new \Exception('JSON数据格式无效');
                }
            } catch (\Exception $e) {
                // JSON解析失败，返回错误
                error_log("JSON解析失败: " . $e->getMessage());
                throw new \Exception('密码数据格式错误');
            }
        } else {
            // 没有JSON数据，返回错误
            throw new \Exception('暂无密码数据');
        }
    }



    /**
     * 获取价格涨跌幅最大的物品 (原始版本备份)
     *
     * @return Response
     */
    /**
     * 获取价格涨跌幅最大的物品列表 (统一使用ba_sjz_price_history表)
     * 返回前10个涨幅最大和前10个跌幅最大的物品
     * 对比当前时间段与24小时前时间段的价格
     *
     * @return Response
     */
    public function getPriceChangeItemsList()
    {
        try {
            // 构建缓存键
            $cacheKey = 'stats:price_change_items_list';

            // 使用 CacheManager 获取数据
            $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
                return $this->fetchPriceChangeItemsList();
            });

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            return json([
                'code' => 0,
                'msg' => '获取价格变动列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取价格变动物品列表数据
     */
    private function fetchPriceChangeItemsList()
    {
        // 定义时间段：当前时间段（最近1小时）vs 24小时前时间段（24-25小时前）
        $currentTime = new \DateTime();

        // 当前时间段：最近1小时的整点时间段
        $currentHourEnd = clone $currentTime;
        $currentHourEnd->setTime($currentTime->format('H'), 0, 0); // 当前小时的开始
        $currentHourStart = clone $currentHourEnd;
        $currentHourStart->modify('-1 hour'); // 前一小时的开始

        // 24小时前时间段：24-25小时前的1小时时间段
        $previousHourEnd = clone $currentHourEnd;
        $previousHourEnd->modify('-24 hours');
        $previousHourStart = clone $previousHourEnd;
        $previousHourStart->modify('-1 hour');

        $currentStart = $currentHourStart->format('Y-m-d H:i:s');
        $currentEnd = $currentHourEnd->format('Y-m-d H:i:s');
        $previousStart = $previousHourStart->format('Y-m-d H:i:s');
        $previousEnd = $previousHourEnd->format('Y-m-d H:i:s');

        // 记录调试信息
        trace("价格对比时间段 - 当前: {$currentStart} 到 {$currentEnd}, 24小时前: {$previousStart} 到 {$previousEnd}");

        // 涨幅最大的物品查询
        $increaseItems = Db::query("
            SELECT
                i.object_id as id,
                i.object_name as name,
                i.pic as image_url,
                i.grade,
                current_prices.current_price,
                previous_prices.previous_price,
                CASE
                    WHEN previous_prices.previous_price > 0 THEN
                        (current_prices.current_price - previous_prices.previous_price) / previous_prices.previous_price
                    ELSE 0
                END as change_rate
            FROM ba_sjz_items i
            INNER JOIN (
                SELECT
                    ph1.object_id,
                    AVG(ph1.price) as current_price
                FROM ba_sjz_price_history ph1
                WHERE ph1.timestamp >= '{$currentStart}'
                AND ph1.timestamp < '{$currentEnd}'
                AND ph1.price > 0
                GROUP BY ph1.object_id
                HAVING COUNT(*) > 0
            ) current_prices ON i.object_id = current_prices.object_id
            INNER JOIN (
                SELECT
                    ph2.object_id,
                    AVG(ph2.price) as previous_price
                FROM ba_sjz_price_history ph2
                WHERE ph2.timestamp >= '{$previousStart}'
                AND ph2.timestamp < '{$previousEnd}'
                AND ph2.price > 0
                GROUP BY ph2.object_id
                HAVING COUNT(*) > 0
            ) previous_prices ON i.object_id = previous_prices.object_id
            WHERE i.delete_time IS NULL
                AND current_prices.current_price > previous_prices.previous_price
                AND previous_prices.previous_price > 0
                AND ABS((current_prices.current_price - previous_prices.previous_price) / previous_prices.previous_price) >= 0.001
            ORDER BY change_rate DESC
            LIMIT 10
        ");

        // 跌幅最大的物品查询
        $decreaseItems = Db::query("
            SELECT
                i.object_id as id,
                i.object_name as name,
                i.pic as image_url,
                i.grade,
                current_prices.current_price,
                previous_prices.previous_price,
                CASE
                    WHEN previous_prices.previous_price > 0 THEN
                        (previous_prices.previous_price - current_prices.current_price) / previous_prices.previous_price
                    ELSE 0
                END as change_rate
            FROM ba_sjz_items i
            INNER JOIN (
                SELECT
                    ph1.object_id,
                    AVG(ph1.price) as current_price
                FROM ba_sjz_price_history ph1
                WHERE ph1.timestamp >= '{$currentStart}'
                AND ph1.timestamp < '{$currentEnd}'
                AND ph1.price > 0
                GROUP BY ph1.object_id
                HAVING COUNT(*) > 0
            ) current_prices ON i.object_id = current_prices.object_id
            INNER JOIN (
                SELECT
                    ph2.object_id,
                    AVG(ph2.price) as previous_price
                FROM ba_sjz_price_history ph2
                WHERE ph2.timestamp >= '{$previousStart}'
                AND ph2.timestamp < '{$previousEnd}'
                AND ph2.price > 0
                GROUP BY ph2.object_id
                HAVING COUNT(*) > 0
            ) previous_prices ON i.object_id = previous_prices.object_id
            WHERE i.delete_time IS NULL
                AND current_prices.current_price < previous_prices.previous_price
                AND previous_prices.previous_price > 0
                AND ABS((current_prices.current_price - previous_prices.previous_price) / previous_prices.previous_price) >= 0.001
            ORDER BY change_rate DESC
            LIMIT 10
        ");

        // 记录调试信息
        trace("价格变动查询结果 - 上涨物品数量: " . count($increaseItems) . ", 下跌物品数量: " . count($decreaseItems));

        // 格式化数据
        foreach ($increaseItems as &$item) {
            $item['increase_rate'] = (float)$item['change_rate'];
            $item['change_type'] = 'increase';
            $item['current_price'] = (float)$item['current_price'];
            $item['previous_price'] = (float)$item['previous_price'];
            $item['grade'] = (int)$item['grade']; // 确保等级为整数
            unset($item['change_rate']);
        }

        foreach ($decreaseItems as &$item) {
            $item['decrease_rate'] = (float)$item['change_rate'];
            $item['change_type'] = 'decrease';
            $item['current_price'] = (float)$item['current_price'];
            $item['previous_price'] = (float)$item['previous_price'];
            $item['grade'] = (int)$item['grade']; // 确保等级为整数
            unset($item['change_rate']);
        }

        return [
            'increase_items' => $increaseItems,
            'decrease_items' => $decreaseItems
        ];
    }










    /**
     * 获取价格涨跌幅最大的物品 (统一使用ba_sjz_price_history表)
     *
     * @return Response
     */
    public function getPriceChangeItems1()
    {
        try {
            // 构建缓存键
            $cacheKey = 'stats:price_change_items';

            // 使用 CacheManager 获取数据
            $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
                return $this->fetchPriceChangeItems1();
            });



            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $data
            ]);

        } catch (\Throwable $e) {
            \think\facade\Log::error('获取价格变动数据异常: ' . $e->getMessage() . "\n堆栈：" . $e->getTraceAsString());
            return json([
                'code' => 0,
                'msg' => '获取价格变动数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取价格变动数据
     */
    private function fetchPriceChangeItems1()
    {
        $highestIncreaseItem = null;
        $highestDecreaseItem = null;

        // 统一使用ba_sjz_price_history表查询，与其他方法保持一致
        $queryStartTime = microtime(true);
        $startDate = date('Y-m-d H:i:s', strtotime('-24 hour'));

        // 查询涨幅最大的物品
        $increaseQuery = "
            SELECT
                i.object_id,
                current_prices.current_price,
                previous_prices.previous_price,
                CASE
                    WHEN previous_prices.previous_price > 0 THEN
                        (current_prices.current_price - previous_prices.previous_price) / previous_prices.previous_price
                    ELSE 0
                END as increase_rate
            FROM ba_sjz_items i
            INNER JOIN (
                SELECT
                    ph1.object_id,
                    (SELECT price FROM ba_sjz_price_history
                     WHERE object_id = ph1.object_id
                     AND price > 0
                     ORDER BY timestamp DESC
                     LIMIT 1) as current_price
                FROM ba_sjz_price_history ph1
                WHERE ph1.timestamp >= '{$startDate}'
                AND ph1.price > 0
                GROUP BY ph1.object_id
            ) current_prices ON i.object_id = current_prices.object_id
            INNER JOIN (
                SELECT
                    ph2.object_id,
                    MAX(ph2.price) as previous_price
                FROM ba_sjz_price_history ph2
                WHERE ph2.timestamp < '{$startDate}'
                AND ph2.price > 0
                GROUP BY ph2.object_id
            ) previous_prices ON i.object_id = previous_prices.object_id
            WHERE i.delete_time IS NULL
                AND current_prices.current_price > previous_prices.previous_price
                AND previous_prices.previous_price > 0
            ORDER BY increase_rate DESC
            LIMIT 1
        ";

        // 查询跌幅最大的物品
        $decreaseQuery = "
            SELECT
                i.object_id,
                current_prices.current_price,
                previous_prices.previous_price,
                CASE
                    WHEN previous_prices.previous_price > 0 THEN
                        (previous_prices.previous_price - current_prices.current_price) / previous_prices.previous_price
                    ELSE 0
                END as decrease_rate
            FROM ba_sjz_items i
            INNER JOIN (
                SELECT
                    ph1.object_id,
                    (SELECT price FROM ba_sjz_price_history
                     WHERE object_id = ph1.object_id
                     AND price > 0
                     ORDER BY timestamp DESC
                     LIMIT 1) as current_price
                FROM ba_sjz_price_history ph1
                WHERE ph1.timestamp >= '{$startDate}'
                AND ph1.price > 0
                GROUP BY ph1.object_id
            ) current_prices ON i.object_id = current_prices.object_id
            INNER JOIN (
                SELECT
                    ph2.object_id,
                    MAX(ph2.price) as previous_price
                FROM ba_sjz_price_history ph2
                WHERE ph2.timestamp < '{$startDate}'
                AND ph2.price > 0
                GROUP BY ph2.object_id
            ) previous_prices ON i.object_id = previous_prices.object_id
            WHERE i.delete_time IS NULL
                AND current_prices.current_price < previous_prices.previous_price
                AND previous_prices.previous_price > 0
            ORDER BY decrease_rate DESC
            LIMIT 1
        ";

        // 执行查询
        $increaseResult = Db::query($increaseQuery);
        $decreaseResult = Db::query($decreaseQuery);
        $queryTime = round((microtime(true) - $queryStartTime) * 1000, 2);

        // 处理涨幅结果
        if (!empty($increaseResult)) {
            $item = $increaseResult[0];
            $highestIncreaseItem = [
                'object_id' => $item['object_id'],
                'current_price' => $item['current_price'],
                'previous_price' => $item['previous_price'],
                'increase_rate' => $item['increase_rate']
            ];
        }

        // 处理跌幅结果
        if (!empty($decreaseResult)) {
            $item = $decreaseResult[0];
            $highestDecreaseItem = [
                'object_id' => $item['object_id'],
                'current_price' => $item['current_price'],
                'previous_price' => $item['previous_price'],
                'decrease_rate' => $item['decrease_rate']
            ];
        }

        \think\facade\Log::info("价格变动查询完成，耗时: {$queryTime}ms，使用ba_sjz_price_history表");

        // 获取物品详细信息
        $this->enrichItemDetails($highestIncreaseItem, $highestDecreaseItem);

        return [
            'highest_increase_item' => $highestIncreaseItem,
            'highest_decrease_item' => $highestDecreaseItem
        ];
    }



    /**
     * 丰富物品详细信息
     *
     * @param array|null &$highestIncreaseItem 涨幅最大物品（引用传递）
     * @param array|null &$highestDecreaseItem 跌幅最大物品（引用传递）
     */
    private function enrichItemDetails(&$highestIncreaseItem, &$highestDecreaseItem)
    {
        try {
            // 收集需要查询的物品ID
            $objectIds = [];
            if ($highestIncreaseItem) {
                $objectIds[] = $highestIncreaseItem['object_id'];
            }
            if ($highestDecreaseItem) {
                $objectIds[] = $highestDecreaseItem['object_id'];
            }

            if (empty($objectIds)) {
                return;
            }

            // 批量查询物品详细信息
            $items = Db::name('sjz_items')
                ->whereIn('object_id', $objectIds)
                ->field('object_id as id, object_name as name, pic as image_url')
                ->select()
                ->toArray();

            // 创建物品信息映射
            $itemMap = [];
            foreach ($items as $item) {
                $itemMap[$item['id']] = $item;
            }

            // 丰富涨幅最大物品信息
            if ($highestIncreaseItem && isset($itemMap[$highestIncreaseItem['object_id']])) {
                $itemInfo = $itemMap[$highestIncreaseItem['object_id']];
                $highestIncreaseItem['id'] = $itemInfo['id'];
                $highestIncreaseItem['name'] = $itemInfo['name'];
                $highestIncreaseItem['image_url'] = $itemInfo['image_url'];
            }

            // 丰富跌幅最大物品信息
            if ($highestDecreaseItem && isset($itemMap[$highestDecreaseItem['object_id']])) {
                $itemInfo = $itemMap[$highestDecreaseItem['object_id']];
                $highestDecreaseItem['id'] = $itemInfo['id'];
                $highestDecreaseItem['name'] = $itemInfo['name'];
                $highestDecreaseItem['image_url'] = $itemInfo['image_url'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('丰富物品详细信息失败: ' . $e->getMessage());
        }
    }
}