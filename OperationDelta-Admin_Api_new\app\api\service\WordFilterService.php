<?php

namespace app\api\service;

use app\common\model\sjz\BlockedWords;
use think\facade\Cache;
use think\facade\Config;

/**
 * 屏蔽词过滤服务
 */
class WordFilterService
{
    /**
     * 缓存键
     */
    protected const CACHE_KEY = 'sjz_blocked_words';
    
    /**
     * 缓存时间（秒）
     */
    protected const CACHE_TTL = 3600; // 1小时
    
    /**
     * 从缓存或数据库获取屏蔽词列表
     * @param string|null $category 分类
     * @return array
     */
    public function getBlockedWords(?string $category = null): array
    {
        $cacheKey = self::CACHE_KEY;
        if ($category) {
            $cacheKey .= ":{$category}";
        }
        
        // 尝试从缓存获取
        $words = Cache::get($cacheKey);
        
        if ($words === null) {
            // 缓存未命中，从数据库获取
            $words = BlockedWords::getAllWords($category);
            // 写入缓存
            Cache::set($cacheKey, $words, self::CACHE_TTL);
        }
        
        return $words;
    }
    
    /**
     * 清除屏蔽词缓存
     * @return bool
     */
    public function clearCache(): bool
    {
        return Cache::delete(self::CACHE_KEY);
    }
    
    /**
     * 检查文本是否包含屏蔽词
     * @param string $text 待检查文本
     * @param string|null $category 屏蔽词分类
     * @return array 检查结果
     */
    public function checkText(string $text, ?string $category = null): array
    {
        $words = $this->getBlockedWords($category);
        $result = [
            'contains' => false,
            'matched_words' => []
        ];
        
        foreach ($words as $word) {
            if ($this->matchWord($text, $word)) {
                $result['contains'] = true;
                $result['matched_words'][] = $word['word'];
            }
            
            // 如果已经匹配到了，可以提前结束
            if ($result['contains'] && Config::get('filter.early_stop', true)) {
                break;
            }
        }
        
        return $result;
    }
    
    /**
     * 过滤文本中的屏蔽词
     * @param string $text 待过滤文本
     * @param string|null $category 屏蔽词分类
     * @return string 过滤后的文本
     */
    public function filterText(string $text, ?string $category = null): string
    {
        $words = $this->getBlockedWords($category);
        
        foreach ($words as $word) {
            $text = $this->replaceWord($text, $word);
        }
        
        return $text;
    }
    
    /**
     * 检查文本并返回违规详情
     * @param string $text 待检查文本
     * @param string|null $category 屏蔽词分类
     * @return array 包含是否违规和详细信息
     */
    public function validateText(string $text, ?string $category = null): array
    {
        $result = $this->checkText($text, $category);
        
        return [
            'valid' => !$result['contains'],
            'message' => $result['contains'] ? '内容包含违禁词' : '',
            'matched_words' => $result['matched_words']
        ];
    }
    
    /**
     * 匹配单个屏蔽词
     * @param string $text 文本
     * @param array $word 词条信息
     * @return bool
     */
    protected function matchWord(string $text, array $word): bool
    {
        // 如果是正则表达式
        if ($word['is_regex']) {
            return preg_match('/' . $word['word'] . '/u', $text) > 0;
        }
        
        // 普通字符串匹配(不区分大小写)
        return mb_stripos($text, $word['word']) !== false;
    }
    
    /**
     * 替换单个屏蔽词
     * @param string $text 文本
     * @param array $word 词条信息
     * @return string
     */
    protected function replaceWord(string $text, array $word): string
    {
        $replacement = $word['replace_with'] ?? '***';
        
        if ($word['is_regex']) {
            return preg_replace('/' . $word['word'] . '/u', $replacement, $text);
        }
        
        return str_ireplace($word['word'], $replacement, $text);
    }
} 