<?php

namespace app\api\controller;

use app\common\model\sjz\Items as ItemsModel;
use app\common\model\sjz\item\Pricehistory;
use app\common\model\sjz\item\Categories as CategoriesModel;
use app\common\model\sjz\item\LatestPrices;
use app\common\controller\Frontend;
use app\api\service\CacheManager;
use app\api\service\ResponseAdapter;
use think\facade\Log;
use think\facade\Db;
use think\facade\Cache;
use think\App;

/**
 * 物品管理控制器 - 重构版本
 * 
 * 重构记录:
 * 2025-07-27: 全面重构
 *   - 统一使用Model方法替代原始SQL查询
 *   - 优化缓存策略，统一使用CacheManager
 *   - 大幅简化代码结构，提高可维护性
 *   - 充分利用Model的关联关系
 */
class Items extends Frontend
{
    protected array $noNeedLogin = ['getItemsList', 'getDetail', 'getPriceHistory', 'getCategories'];
    protected array $noNeedPermission = [];

    private CacheManager $cacheManager;

    // 分页常量
    private const MAX_PAGE_SIZE = 100;
    private const DEFAULT_PAGE_SIZE = 20;
    private const CACHE_TTL_ITEM_DETAIL = 3600;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cacheManager = new CacheManager();
    }

    /**
     * 获取物品列表
     *
     * @return json
     */
    public function getItemsList()
    {
        try {
            // 获取并验证参数
            $params = $this->getValidatedParams();
            if (!$params['success']) {
                return ResponseAdapter::error($params['msg']);
            }

            // 构建缓存键
            $cacheKey = 'items_list:' . md5(json_encode($params['data']));
            
            // 尝试从缓存获取数据
            $result = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($params) {
                return $this->fetchItemsListData($params['data']);
            });

            return ResponseAdapter::success('获取成功', $result);

        } catch (\Exception $e) {
            Log::error("getItemsList error: {$e->getMessage()}");
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取并验证参数
     */
    private function getValidatedParams(): array
    {
        $params = [
            'page' => input('page/d', 1),
            'pageSize' => input('pageSize/d', self::DEFAULT_PAGE_SIZE),
            'search' => input('search/s', ''),
            'category' => input('category/s', 'all'),
            'propType' => input('propType/s', ''),
            'minPrice' => input('minPrice/f'),
            'maxPrice' => input('maxPrice/f'),
            'grade' => input('grade/s', 'all'),
            'sort' => input('sort/s', 'default')
        ];

        if ($params['page'] < 1) {
            return ['success' => false, 'msg' => '页码不能小于1'];
        }
        if ($params['pageSize'] < 1 || $params['pageSize'] > self::MAX_PAGE_SIZE) {
            return ['success' => false, 'msg' => '每页数量必须在1-100之间'];
        }

        return ['success' => true, 'data' => $params];
    }

    /**
     * 获取物品列表数据 - 性能优化版本
     */
    private function fetchItemsListData(array $params): array
    {
        extract($params);

        // 构建基础查询
        $query = ItemsModel::alias('i')
            ->field([
                'i.id',
                'i.object_id',
                'i.object_name',
                'i.category_id',
                'i.grade',
                'i.pic',
                'i.update_time'
            ])
            ->where('i.delete_time', 'null');

        // 添加搜索条件
        if ($search) {
            $this->applySearchConditions($query, $search);
        }

        // 添加分类条件
        if ($category && $category !== 'all') {
            $this->applyCategoryFilter($query, $category);
        }

        // 添加道具类型条件
        if (!empty($propType)) {
            $query->join('sjz_prop_details pd', 'i.object_id = pd.object_id', 'LEFT')
                  ->where('pd.type', $propType);
        }

        // 添加等级条件
        $this->applyGradeFilter($query, $grade);

        // 处理价格排序和过滤
        if ($this->needsPriceJoin($minPrice, $maxPrice, $sort)) {
            $this->applyPriceFiltersAndSort($query, $minPrice, $maxPrice, $sort);
        } else {
            $query->order('i.id', 'desc');
        }

        // 获取总记录数
        $total = $query->count();

        if ($total == 0) {
            return $this->buildEmptyResult($page, $pageSize);
        }

        // 应用分页并获取数据
        $items = $query->limit(($page - 1) * $pageSize, $pageSize)->select()->toArray();

        // 手动加载分类信息
        if (!empty($items)) {
            $this->loadCategoriesForItems($items);
        }

        // 获取价格信息
        $this->attachPriceInfo($items);

        return [
            'items' => $items,
            'total' => $total,
            'pagination' => [
                'current' => (int)$page,
                'pageSize' => (int)$pageSize,
                'total' => (int)$total,
                'pages' => (int)ceil($total / $pageSize)
            ]
        ];
    }

    /**
     * 应用分类过滤 - 简化优化版本
     */
    private function applyCategoryFilter($query, string $category): void
    {
        $query->join('sjz_item_categories c', 'i.category_id = c.id')
              ->where(function($q) use ($category) {
                  $q->where('c.primary_class', $category)
                    ->whereOr('c.second_class', $category)
                    ->whereOr('c.category_key', $category);
              });
    }

    /**
     * 应用等级过滤
     */
    private function applyGradeFilter($query, string $grade): void
    {
        if (is_numeric($grade)) {
            $query->where('i.grade', intval($grade));
        } elseif ($grade !== 'all') {
            // 处理逗号分隔的等级值（如 "0,1"）
            if (strpos($grade, ',') !== false) {
                $grades = array_map('intval', explode(',', $grade));
                $query->whereIn('i.grade', $grades);
            } elseif (preg_match('/\((\d+)\)/', $grade, $matches)) {
                $query->where('i.grade', intval($matches[1]));
            }
        }
    }

    /**
     * 判断是否需要价格联表查询
     */
    private function needsPriceJoin($minPrice, $maxPrice, string $sort): bool
    {
        return $minPrice !== null || $maxPrice !== null || in_array($sort, ['price_asc', 'price_desc']);
    }

    /**
     * 应用价格过滤和排序 - 高性能版本
     */
    private function applyPriceFiltersAndSort($query, $minPrice, $maxPrice, string $sort): void
    {
        // 使用最新价格表进行JOIN
        $query->join('ba_sjz_latest_prices lp', 'i.object_id = lp.object_id', 'INNER');

        // 应用价格过滤
        if ($minPrice !== null) {
            $query->where('lp.current_price', '>=', $minPrice);
        }
        if ($maxPrice !== null) {
            $query->where('lp.current_price', '<=', $maxPrice);
        }

        // 应用排序
        if ($sort === 'price_asc') {
            $query->order('lp.current_price', 'asc');
        } elseif ($sort === 'price_desc') {
            $query->order('lp.current_price', 'desc');
        }
    }

    /**
     * 检查最新价格表是否存在
     */
    private function checkLatestPriceTableExists(): bool
    {
        static $tableExists = null;

        if ($tableExists === null) {
            try {
                Db::query("SELECT 1 FROM ba_sjz_latest_prices LIMIT 1");
                $tableExists = true;
            } catch (\Exception $e) {
                $tableExists = false;
            }
        }

        return $tableExists;
    }

    /**
     * 构建空结果
     */
    private function buildEmptyResult(int $page, int $pageSize): array
    {
        return [
            'items' => [],
            'total' => 0,
            'pagination' => [
                'current' => $page,
                'pageSize' => $pageSize,
                'total' => 0,
                'pages' => 0
            ]
        ];
    }

    /**
     * 附加价格信息 - 优化版本
     */
    private function attachPriceInfo(array &$items): void
    {
        $objectIds = array_column($items, 'object_id');
        if (empty($objectIds)) {
            return;
        }

        // 使用 LatestPrices 模型获取价格数据
        $pricesData = LatestPrices::getBatchPrices($objectIds);

        $currentPrices = [];
        $oldPrices = [];

        foreach ($pricesData as $objectId => $priceInfo) {
            $currentPrices[$objectId] = $priceInfo['current_price'];
            // 如果24小时前价格存在，使用它；否则使用当前价格
            $oldPrices[$objectId] = $priceInfo['price_24h_ago'] !== null
                ? $priceInfo['price_24h_ago']
                : $priceInfo['current_price'];
        }

        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            $latestPrice = $currentPrices[$objectId] ?? 0;
            $oldPrice = $oldPrices[$objectId] ?? $latestPrice;

            $item['price_change'] = ['price' => round($latestPrice, 2)];
            $item['price_24h_ago'] = round($latestPrice - $oldPrice, 2);
        }
    }

    /**
     * 批量获取24小时前价格
     */
    private function getBatch24hAgoPrices(array $objectIds, string $time24hAgo): array
    {
        if (empty($objectIds)) {
            return [];
        }

        $cacheKey = 'batch_24h_prices:' . md5(implode(',', $objectIds) . ':' . $time24hAgo);
        
        return $this->cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($objectIds, $time24hAgo) {
            $result = Pricehistory::whereIn('object_id', $objectIds)
                ->where('timestamp', '<=', $time24hAgo)
                ->field('object_id, price')
                ->order('timestamp', 'desc')
                ->group('object_id')
                ->select()
                ->column('price', 'object_id');
            return $result ?: [];
        });
    }

    /**
     * 计算到下一个整点前1分钟的秒数
     */
    private function getSecondsToNextHourMinus1Minute(): int
    {
        $now = time();
        $nextHour = strtotime(date('Y-m-d H:00:00', $now + 3600));
        $oneMinuteBefore = $nextHour - 60;
        return max(60, $oneMinuteBefore - $now); // 最少缓存1分钟
    }

    /**
     * 获取物品详细信息
     *
     * @return json
     *
     * 接口功能：
     * 1. 获取物品的基本信息
     * 2. 获取物品的分类信息
     * 3. 获取物品的额外详细信息
     * 4. 获取物品的弹药信息
     * 5. 获取物品的配件槽信息
     * 6. 获取物品的最新价格和24小时价格变化
     *
     * 请求参数：
     * @param int $object_id 物品ID，必填
     *
     * 返回数据：
     * @return array {
     *   code: int,    // 状态码：1成功，0失败
     *   msg:  string, // 提示信息
     *   data: {
     *     id: int,           // 物品ID
     *     object_id: int,    // 物品标识ID
     *     object_name: string,// 物品名称
     *     category_id: int,  // 分类ID
     *     grade: int,        // 等级
     *     length: int,       // 长度
     *     width: int,        // 宽度
     *     weight: float,     // 重量
     *     pic: string,       // 图片URL
     *     pre_pic: string,   // 预览图URL
     *     desc: string,      // 描述
     *     category: {        // 分类信息
     *       id: int,
     *       category_key: string,
     *       primary_class: string,
     *       second_class: string,
     *       second_class_cn: string
     *     },
     *     extraDetails: [{   // 额外详细信息
     *       // 动态字段
     *     }],
     *     ammoList: [{      // 弹药信息
     *       // 动态字段
     *     }],
     *     accessorySlots: [{ // 配件槽信息
     *       // 动态字段
     *     }],
     *     protectDetails: { // 防具详情
     *       // 动态字段
     *     },
     *     propDetails: { // 道具详情
     *       // 动态字段
     *     },
     *     price_change: {    // 价格信息
     *       price: float     // 当前价格
     *     },
     *     price_24h_ago: float// 24小时价格变化（正数表示上涨，负数表示下跌）
     *   }
     * }
     */
    public function getDetail()
    {
        $objectId = input('post.object_id/d');
        if (!$objectId) {
            return json(['code' => 0, 'msg' => '参数错误：物品ID不能为空', 'data' => null]);
        }

        // 构建缓存键
        $cacheKey = 'item:detail:' . $objectId;

        // 使用缓存管理器获取数据
        $itemData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectId) {
            return $this->getItemDetailData($objectId);
        });
        
        if ($itemData !== null) {
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $itemData
            ]);
        }

        return json([
            'code' => 0,
            'msg' => '物品不存在',
            'data' => null
        ]);
    }

    /**
     * 获取物品价格历史
     *
     * 请求参数：
     * - object_id: int 物品ID（必填）
     * - range: string 时间范围，可选值：24h, 3d, 7d, 30d（默认：24h）
     *
     * 返回参数：
     * - code: int 状态码，1表示成功，0表示失败
     * - msg: string 提示信息
     * - data: object 数据对象
     *   - history: array 价格历史数据
     *     - time: string 时间点
     *     - price: float 价格
     *   - statistics: object 统计数据
     *     - min_price: float 最低价格
     *     - max_price: float 最高价格
     *     - avg_price: float 平均价格
     *     - start_price: float 起始价格
     *     - end_price: float 结束价格
     *     - change_value: float 价格变化值
     *     - change_percent: float 价格变化百分比
     *
     * 优化点：
     * 1. 返回所有原始数据点，不进行采样处理
     * 2. 优化SQL查询，使用单个查询获取统计数据
     * 3. 添加性能监控和详细日志
     * 4. 改进缓存策略，根据数据特性调整缓存时间
     * 5. 增强错误处理和参数验证
     * 6. 支持大数据量查询，保持数据完整性
     */
    public function getPriceHistory()
    {
        $requestStartTime = microtime(true);

        try {
            $objectId = input('post.object_id/d');
            $range = input('post.range/s', '24h');
            $limit = input('post.limit/d', 0); // 默认0表示不限制，返回所有数据

            // 参数验证
            if (!$objectId || $objectId <= 0) {
                return json(['code' => 0, 'msg' => '物品ID参数错误', 'data' => null]);
            }

            // limit参数验证：0表示不限制，大于0时设置合理上限
            if ($limit < 0 || $limit > 50000) {
                return json(['code' => 0, 'msg' => '数据点限制参数错误（0表示不限制，最大50000）', 'data' => null]);
            }

            // 验证时间范围
            $validRanges = ['24h', '3d', '7d', '30d', '60d', '90d', '180d'];
            if (!in_array($range, $validRanges)) {
                return json(['code' => 0, 'msg' => '无效的时间范围', 'data' => null]);
            }

            // 构建智能缓存键，包含版本信息
            $cacheVersion = 'v2'; // 缓存版本，用于强制更新缓存结构
            $cacheKey = "item:price_history:{$cacheVersion}:{$objectId}:{$range}:{$limit}";

            // 使用缓存管理器获取数据
            $historyData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectId, $range, $limit) {
                return $this->getPriceHistoryData($objectId, $range, $limit);
            });

            $totalExecutionTime = round((microtime(true) - $requestStartTime) * 1000, 2);

            if ($historyData !== null && !empty($historyData['history'])) {
                // 记录成功的请求统计
                Log::info("价格历史请求成功", [
                    'object_id' => $objectId,
                    'range' => $range,
                    'limit' => $limit,
                    'data_points' => count($historyData['history']),
                    'total_time_ms' => $totalExecutionTime
                ]);

                return json([
                    'code' => 1,
                    'msg' => '获取成功',
                    'data' => $historyData,
                    'meta' => [
                        'execution_time_ms' => $totalExecutionTime,
                        'data_points' => count($historyData['history'])
                    ]
                ]);
            }

            // 数据为空的情况
            Log::warning("价格历史数据为空", [
                'object_id' => $objectId,
                'range' => $range,
                'total_time_ms' => $totalExecutionTime
            ]);

            return json([
                'code' => 1,
                'msg' => '暂无价格历史数据',
                'data' => [
                    'history' => [],
                    'statistics' => [
                        'min_price' => 0,
                        'max_price' => 0,
                        'avg_price' => 0,
                        'start_price' => 0,
                        'end_price' => 0,
                        'change_value' => 0,
                        'change_percent' => 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            $totalExecutionTime = round((microtime(true) - $requestStartTime) * 1000, 2);

            Log::error('获取价格历史异常', [
                'object_id' => $objectId ?? 'unknown',
                'range' => $range ?? 'unknown',
                'error' => $e->getMessage(),
                'total_time_ms' => $totalExecutionTime,
                'trace' => $e->getTraceAsString()
            ]);

            return json([
                'code' => 0,
                'msg' => '服务器内部错误，请稍后重试',
                'data' => null
            ]);
        }
    }

    /**
     * 获取物品分类列表
     *
     * @return json
     *
     * 接口功能：
     * 1. 获取所有物品分类及其子分类
     * 2. 返回分类的层级结构
     *
     * 优化点：
     * 1. 优化查询，减少内存使用
     * 2. 使用预处理数据结构，减少循环次数
     */
    public function getCategories()
    {
        try {
            // 构建缓存键
            $cacheKey = 'categories';
            
            // 使用缓存管理器获取数据
            $result = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_STATIC_DATA, function() {
                return $this->fetchCategoriesData();
            });

            // 保持原有响应格式
            return ResponseAdapter::success('获取成功', $result);

        } catch (\Exception $e) {
            Log::error('getCategories error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取分类数据的核心逻辑
     */
    private function fetchCategoriesData(): array
    {
        // 使用模型查询替代原来的 Db 查询
        $queryStartTime = microtime(true);
        $categories = CategoriesModel::field([
                'id',
                'category_key',
                'primary_class',
                'second_class',
                'second_class_cn',
                'item_count'
            ])
            ->where('delete_time', 'null')
            ->order(['primary_class' => 'asc', 'second_class' => 'asc'])
            ->select()
            ->toArray();

        if (empty($categories)) {
            return [
                'categories' => [],
                'total' => 0
            ];
        }

        // 优化：使用单次循环构建分类树
        $processStartTime = microtime(true);
        $result = [];
        $primaryClassMap = []; // 用于快速查找主分类索引

        foreach ($categories as $category) {
            $primaryClass = $category['primary_class'];

            // 如果主分类不存在，创建它
            if (!isset($primaryClassMap[$primaryClass])) {
                $primaryClassMap[$primaryClass] = count($result);
                $result[] = [
                    'primary_class' => $primaryClass,
                    'total_items' => 0,
                    'children' => []
                ];
            }

            // 获取主分类的索引
            $index = $primaryClassMap[$primaryClass];

            // 添加二级分类
            $result[$index]['children'][] = [
                'id' => $category['id'],
                'category_key' => $category['category_key'],
                'second_class' => $category['second_class'],
                'second_class_cn' => $category['second_class_cn'],
                'item_count' => $category['item_count']
            ];

            // 更新主分类的总物品数
            $result[$index]['total_items'] += $category['item_count'];
        }

        // 准备返回数据
        return [
            'categories' => $result,
            'total' => count($categories)
        ];
    }

    /**
     * 批量获取物品价格
     *
     * 请求参数：
     * - object_ids: array 物品ID数组（必填，最多100个）
     *
     * 返回参数：
     * - code: int 状态码，1表示成功，0表示失败
     * - msg: string 提示信息
     * - data: object 数据对象，键为物品ID，值为价格信息
     *   - price: float 当前价格
     *   - price_24h_ago: float 24小时价格变化
     *
     * 优化点：
     * 1. 使用批量查询减少数据库连接次数
     * 2. 限制单次请求的物品数量，避免查询过大
     */
    public function getBatchPrices()
    {
        try {
            $objectIds = input('post.object_ids/a');

            if (empty($objectIds)) {
                return json(['code' => 0, 'msg' => '参数错误：物品ID不能为空', 'data' => null]);
            }

            // 限制单次请求的物品数量
            if (count($objectIds) > 100) {
                return json(['code' => 0, 'msg' => '参数错误：单次最多查询100个物品', 'data' => null]);
            }

            // 排序ID以保证缓存键的一致性
            sort($objectIds);

            // 构建缓存键
            $cacheKey = 'items:batch_prices:' . md5(json_encode($objectIds));

            // 使用缓存管理器获取数据
            $batchData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectIds) {
                return $this->getBatchPricesData($objectIds);
            });
            
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $batchData
            ]);

        } catch (\Exception $e) {
            Log::error('批量获取价格异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 0, 'msg' => '获取失败', 'data' => null]);
        }
    }

    /**
     * 从最新价格表获取价格数据
     *
     * @param array $objectIds 物品ID数组
     * @return array 包含当前价格和24小时前价格的数组
     */
    private function getLatestPricesFromTable(array $objectIds): array
    {
        $result = [
            'current_prices' => [],
            'old_prices' => []
        ];

        if (empty($objectIds)) {
            return $result;
        }

        try {
            // 检查最新价格表是否存在
            $tableExists = false;
            try {
                Db::query("SELECT 1 FROM ba_sjz_latest_prices LIMIT 1");
                $tableExists = true;
            } catch (\Exception $e) {
                // 表不存在，使用原来的方法
                $tableExists = false;
            }

            if ($tableExists) {
                // 使用 LatestPrices 模型获取数据
                $pricesData = LatestPrices::getBatchPrices($objectIds);

                foreach ($pricesData as $objectId => $priceInfo) {
                    $result['current_prices'][$objectId] = $priceInfo['current_price'];

                    // 如果24小时前价格存在，使用它；否则使用当前价格
                    if ($priceInfo['price_24h_ago'] !== null) {
                        $result['old_prices'][$objectId] = $priceInfo['price_24h_ago'];
                    } else {
                        $result['old_prices'][$objectId] = $priceInfo['current_price'];
                    }
                }

                // 如果有些物品在最新价格表中没有数据，使用原来的方法获取
                $missingObjectIds = array_diff($objectIds, array_keys($result['current_prices']));

                if (!empty($missingObjectIds)) {
                    // 使用原来的方法获取缺失的价格数据
                    $this->getLatestPricesFromHistory($missingObjectIds, $result);
                }
            } else {
                // 如果最新价格表不存在，使用原来的方法
                $this->getLatestPricesFromHistory($objectIds, $result);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('从最新价格表获取价格数据异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // 出错时使用原来的方法
            $this->getLatestPricesFromHistory($objectIds, $result);
            return $result;
        }
    }

    /**
     * 从价格历史表获取价格数据（原来的方法）
     *
     * @param array $objectIds 物品ID数组
     * @param array &$result 结果数组的引用
     */
    private function getLatestPricesFromHistory(array $objectIds, array &$result): void
    {
        if (empty($objectIds)) {
            return;
        }

        try {
            // 获取当前价格
            $currentPrices = Pricehistory::getBatchLatestPrices($objectIds);
            foreach ($currentPrices as $objectId => $price) {
                $result['current_prices'][$objectId] = $price;
            }

            // 获取24小时前价格
            $time24hAgo = date('Y-m-d H:i:s', strtotime('-24 hours'));
            $oldPricesCacheKey = 'items:old_prices:' . md5(implode(',', $objectIds) . ':' . $time24hAgo);

            $oldPrices = $this->cacheManager->remember($oldPricesCacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectIds, $time24hAgo) {
                return $this->getOldPricesFromDatabase($objectIds, $time24hAgo);
            });

            foreach ($oldPrices as $objectId => $price) {
                $result['old_prices'][$objectId] = $price;
            }

            // 对于没有24小时前价格的物品，使用当前价格
            foreach ($result['current_prices'] as $objectId => $price) {
                if (!isset($result['old_prices'][$objectId])) {
                    $result['old_prices'][$objectId] = $price;
                }
            }

        } catch (\Exception $e) {
            Log::error('从价格历史表获取价格数据异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }

    /**
     * 获取物品详情数据的核心逻辑 - 优化版本
     *
     * 优化点：
     * 1. 分层缓存策略：基础数据和价格数据分别缓存
     * 2. 批量查询优化：减少数据库查询次数
     * 3. 异步价格更新：价格数据异步获取，不阻塞主流程
     * 4. 智能预加载：根据物品类型动态加载关联数据
     */
    private function getItemDetailData($objectId): ?array
    {
        $startTime = microtime(true);

        try {
            // 1. 尝试从分层缓存获取基础数据
            $baseDataCacheKey = "item:base_data:{$objectId}";
            $baseData = $this->cacheManager->remember($baseDataCacheKey, CacheManager::TYPE_STATIC_DATA, function() use ($objectId) {
                return $this->getItemBaseData($objectId);
            });

            if (!$baseData) {
                return null;
            }

            // 2. 获取实时价格数据（使用独立缓存）
            $priceData = $this->getItemPriceData($objectId);

            // 3. 合并基础数据和价格数据
            $item = array_merge($baseData, $priceData);

            // 4. 根据物品类型异步加载扩展数据
            $item = $this->loadExtendedData($item, $objectId);

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("物品详情查询完成", [
                'object_id' => $objectId,
                'execution_time_ms' => $executionTime,
                'has_extended_data' => isset($item['ammoDetailsList']) || isset($item['bulletDetails']) || isset($item['accessoryDetails'])
            ]);

            return $item;

        } catch (\Exception $e) {
            Log::error('getItemDetailData error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * 获取物品基础数据（相对静态，缓存时间较长）
     */
    private function getItemBaseData($objectId): ?array
    {
        // 获取物品基本信息和关联数据 - 使用预加载优化
        $item = ItemsModel::with(['category', 'extraDetails', 'ammoList', 'accessorySlots', 'protectDetails', 'propDetails', 'ammoDetails', 'accessoryDetails'])
            ->where('object_id', $objectId)
            ->find();

        if (!$item) {
            return null;
        }

        return $item->toArray();
    }

    /**
     * 获取物品价格数据（实时性要求高，缓存时间较短）
     */
    private function getItemPriceData($objectId): array
    {
        $priceCacheKey = "item:price_data:{$objectId}";

        return $this->cacheManager->remember($priceCacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectId) {
            // 获取最新价格和24小时前价格
            $pricesData = $this->getLatestPricesFromTable([$objectId]);
            $latestPrice = $pricesData['current_prices'][$objectId] ?? 0;
            $oldPrice = $pricesData['old_prices'][$objectId] ?? $latestPrice;

            // 计算价格变化
            $priceDifference = $latestPrice - $oldPrice;

            return [
                'price_change' => ['price' => round($latestPrice, 2)],
                'price_24h_ago' => round($priceDifference, 2)
            ];
        });
    }

    /**
     * 根据物品类型加载扩展数据
     */
    private function loadExtendedData($item, $objectId): array
    {
        $primaryClass = $item['category']['primary_class'] ?? '';

        switch ($primaryClass) {
            case 'gun':
                $item = $this->loadWeaponExtendedData($item, $objectId);
                break;
            case 'ammo':
                $item = $this->loadAmmoExtendedData($item);
                break;
            case 'acc':
                $item = $this->loadAccessoryExtendedData($item);
                break;
        }

        return $item;

    }

    /**
     * 加载武器类物品的扩展数据
     */
    private function loadWeaponExtendedData($item, $objectId): array
    {
        if (empty($item['ammoList'])) {
            return $item;
        }

        $ammoCacheKey = "item:ammo_details:{$objectId}";

        $ammoDetailsMap = $this->cacheManager->remember($ammoCacheKey, CacheManager::TYPE_STATIC_DATA, function() use ($item) {
            // 收集所有关联的弹药类型ID
            $ammoList = $item['ammoList'] instanceof \think\model\Collection ? $item['ammoList']->toArray() : $item['ammoList'];
            $ammoTypeIds = array_column($ammoList, 'ammo_type');

            if (empty($ammoTypeIds)) {
                return [];
            }

            // 批量查询弹药详情、基本信息和价格
            $ammoObjectIds = $ammoTypeIds; // 假设ammo_type就是object_id

            // 使用单个SQL查询获取所有需要的数据
            $sql = "
                SELECT
                    i.object_id,
                    i.object_name,
                    i.pic,
                    ad.damage,
                    ad.penetration,
                    ad.armor_damage,
                    ad.accuracy,
                    ad.recoil,
                    ad.fragmentation_chance,
                    lp.price
                FROM ba_sjz_items i
                LEFT JOIN ba_sjz_ammo_details ad ON i.object_id = ad.object_id
                LEFT JOIN ba_sjz_latest_prices lp ON i.object_id = lp.object_id
                WHERE i.object_id IN (" . implode(',', array_map('intval', $ammoObjectIds)) . ")
            ";

            $ammoData = Db::query($sql);

            // 创建映射
            $ammoDetailsMap = [];
            foreach ($ammoData as $data) {
                $ammoDetailsMap[$data['object_id']] = $data;
            }

            return $ammoDetailsMap;
        });

        $item['ammoDetailsList'] = $ammoDetailsMap;
        return $item;
    }

    /**
     * 加载弹药类物品的扩展数据
     */
    private function loadAmmoExtendedData($item): array
    {
        if (!empty($item['ammoDetails'])) {
            $ammoDetail = $item['ammoDetails'] instanceof \think\model\Collection ? $item['ammoDetails']->toArray() : $item['ammoDetails'];
            if (is_array($ammoDetail) && !empty($ammoDetail)) {
                $item['bulletDetails'] = $ammoDetail;
            }
        }
        return $item;
    }

    /**
     * 加载配件类物品的扩展数据
     */
    private function loadAccessoryExtendedData($item): array
    {
        if (!empty($item['accessoryDetails'])) {
            $accessoryDetail = $item['accessoryDetails'] instanceof \think\model\Collection ? $item['accessoryDetails']->toArray() : $item['accessoryDetails'];
            if (is_array($accessoryDetail) && !empty($accessoryDetail)) {
                $item['accessoryDetails'] = $accessoryDetail;
            }
        }
        return $item;
    }

    /**
     * 异步预热价格数据
     * 预热所有时间维度的数据到缓存中
     */
    public function preloadPriceData()
    {
        try {
            $objectId = input('post.object_id/d');

            if (!$objectId || $objectId <= 0) {
                return json(['code' => 0, 'msg' => '物品ID参数错误', 'data' => null]);
            }

            // 所有需要预热的时间范围
            $allRanges = ['24h', '3d', '7d', '30d', '60d', '90d', '180d'];
            $preloadResults = [];
            $startTime = microtime(true);

            Log::info("开始预热价格数据", [
                'object_id' => $objectId,
                'ranges' => $allRanges
            ]);

            foreach ($allRanges as $range) {
                $rangeStartTime = microtime(true);

                // 构建缓存键
                $cacheVersion = 'v2';
                $cacheKey = "item:price_history:{$cacheVersion}:{$objectId}:{$range}:0";

                // 检查是否已经缓存
                $existingData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
                    return null; // 如果不存在则返回null
                });

                if ($existingData === null) {
                    // 数据不存在，进行预热
                    $historyData = $this->getPriceHistoryData($objectId, $range, 0);

                    if ($historyData !== null) {
                        // 存储到缓存 - 使用remember方法
                        $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($historyData) {
                            return $historyData;
                        });

                        $rangeEndTime = microtime(true);
                        $preloadResults[$range] = [
                            'status' => 'preloaded',
                            'data_points' => count($historyData['history']),
                            'execution_time_ms' => round(($rangeEndTime - $rangeStartTime) * 1000, 2)
                        ];

                        Log::info("预热完成", [
                            'object_id' => $objectId,
                            'range' => $range,
                            'data_points' => count($historyData['history']),
                            'execution_time_ms' => $preloadResults[$range]['execution_time_ms']
                        ]);
                    } else {
                        $preloadResults[$range] = [
                            'status' => 'failed',
                            'data_points' => 0,
                            'execution_time_ms' => 0
                        ];
                    }
                } else {
                    // 数据已存在
                    $preloadResults[$range] = [
                        'status' => 'cached',
                        'data_points' => count($existingData['history'] ?? []),
                        'execution_time_ms' => 0
                    ];
                }
            }

            $totalExecutionTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info("预热任务完成", [
                'object_id' => $objectId,
                'total_time_ms' => $totalExecutionTime,
                'results' => $preloadResults
            ]);

            return json([
                'code' => 1,
                'msg' => '预热完成',
                'data' => [
                    'object_id' => $objectId,
                    'total_execution_time_ms' => $totalExecutionTime,
                    'preload_results' => $preloadResults
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('预热价格数据异常', [
                'object_id' => $objectId ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json([
                'code' => 0,
                'msg' => '预热失败',
                'data' => null
            ]);
        }
    }

    /**
     * 批量预热物品详情数据
     * 用于热门物品的批量预热
     */
    public function batchPreloadItemDetails()
    {
        try {
            $objectIds = input('post.object_ids/a', []);

            if (empty($objectIds)) {
                return json(['code' => 0, 'msg' => '物品ID列表不能为空', 'data' => null]);
            }

            $startTime = microtime(true);
            $results = [];

            Log::info("开始批量预热物品详情", [
                'object_ids' => $objectIds,
                'count' => count($objectIds)
            ]);

            foreach ($objectIds as $objectId) {
                $itemStartTime = microtime(true);

                // 预热基础数据
                $baseDataCacheKey = "item:base_data:{$objectId}";
                $baseData = $this->cacheManager->remember($baseDataCacheKey, CacheManager::TYPE_STATIC_DATA, function() use ($objectId) {
                    return $this->getItemBaseData($objectId);
                });

                // 预热价格数据
                $priceData = $this->getItemPriceData($objectId);

                $itemEndTime = microtime(true);
                $results[$objectId] = [
                    'status' => $baseData ? 'success' : 'failed',
                    'execution_time_ms' => round(($itemEndTime - $itemStartTime) * 1000, 2),
                    'has_base_data' => $baseData !== null,
                    'has_price_data' => !empty($priceData)
                ];
            }

            $totalExecutionTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info("批量预热完成", [
                'total_items' => count($objectIds),
                'total_time_ms' => $totalExecutionTime,
                'results' => $results
            ]);

            return json([
                'code' => 1,
                'msg' => '批量预热完成',
                'data' => [
                    'total_items' => count($objectIds),
                    'total_execution_time_ms' => $totalExecutionTime,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('批量预热物品详情异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json([
                'code' => 0,
                'msg' => '批量预热失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取价格历史数据的核心逻辑
     *
     * 优化点：
     * 1. 使用单个SQL查询获取统计数据和起始/结束价格
     * 2. 返回所有原始数据点，保持数据完整性
     * 3. 使用索引优化的查询语句
     * 4. 添加性能监控和日志
     * 5. 支持大数据量查询，提供详细的时间戳信息
     */
    private function getPriceHistoryData($objectId, $range, $limit): ?array
    {
        $startTime = microtime(true);

        // 计算时间范围
        $endTime = date('Y-m-d H:i:s');
        $startTimeStr = match($range) {
            '24h' => date('Y-m-d H:i:s', strtotime('-24 hours')),
            '3d' => date('Y-m-d H:i:s', strtotime('-3 days')),
            '7d' => date('Y-m-d H:i:s', strtotime('-7 days')),
            '30d' => date('Y-m-d H:i:s', strtotime('-30 days')),
            '60d' => date('Y-m-d H:i:s', strtotime('-60 days')),
            '90d' => date('Y-m-d H:i:s', strtotime('-90 days')),
            '180d' => date('Y-m-d H:i:s', strtotime('-180 days')),
            default => date('Y-m-d H:i:s', strtotime('-24 hours'))
        };

        // 优化：使用单个SQL查询获取所有统计数据
        $tableName = Pricehistory::getTable();
        $statsAndBoundsSql = "
            SELECT
                MIN(price) as min_price,
                MAX(price) as max_price,
                AVG(price) as avg_price,
                COUNT(*) as count,
                (SELECT price FROM {$tableName}
                 WHERE object_id = {$objectId} AND timestamp >= '{$startTimeStr}'
                 ORDER BY timestamp ASC LIMIT 1) as start_price,
                (SELECT price FROM {$tableName}
                 WHERE object_id = {$objectId} AND timestamp <= '{$endTime}'
                 ORDER BY timestamp DESC LIMIT 1) as end_price
            FROM {$tableName}
            WHERE object_id = {$objectId}
            AND timestamp BETWEEN '{$startTimeStr}' AND '{$endTime}'
        ";

        $statsResult = Db::query($statsAndBoundsSql);
        $stats = $statsResult[0] ?? null;

        if (!$stats || $stats['count'] == 0) {
            Log::info("物品 {$objectId} 在时间范围 {$range} 内无价格数据");
            return [
                'history' => [],
                'statistics' => [
                    'min_price' => 0,
                    'max_price' => 0,
                    'avg_price' => 0,
                    'start_price' => 0,
                    'end_price' => 0,
                    'change_value' => 0,
                    'change_percent' => 0
                ]
            ];
        }

        // 计算变化值和百分比
        $startPrice = (float)($stats['start_price'] ?? 0);
        $endPrice = (float)($stats['end_price'] ?? 0);
        $changeValue = $endPrice - $startPrice;
        $changePercent = $startPrice > 0 ? ($changeValue / $startPrice) * 100 : 0;

        // 准备统计数据
        $statistics = [
            'min_price' => round((float)$stats['min_price'], 2),
            'max_price' => round((float)$stats['max_price'], 2),
            'avg_price' => round((float)$stats['avg_price'], 2),
            'start_price' => round($startPrice, 2),
            'end_price' => round($endPrice, 2),
            'change_value' => round($changeValue, 2),
            'change_percent' => round($changePercent, 2)
        ];

        // 获取所有原始历史数据（不进行采样）
        $dataCount = (int)$stats['count'];
        $history = $this->getAllHistoryData($objectId, $startTimeStr, $endTime, $limit);

        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        Log::info("价格历史查询完成", [
            'object_id' => $objectId,
            'range' => $range,
            'data_count' => $dataCount,
            'returned_points' => count($history),
            'execution_time_ms' => $executionTime
        ]);

        return [
            'history' => $history,
            'statistics' => $statistics
        ];
    }

    /**
     * 获取所有历史数据（不进行采样）
     * 返回指定时间范围内的所有原始数据点
     */
    private function getAllHistoryData($objectId, $startTime, $endTime, $limit): array
    {
        $tableName = Pricehistory::getTable();

        // 查询所有原始数据，按时间排序
        $sql = "SELECT
                    DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:%s') as time,
                    ROUND(price, 2) as price,
                    timestamp as raw_timestamp
                FROM {$tableName}
                WHERE object_id = {$objectId}
                AND timestamp BETWEEN '{$startTime}' AND '{$endTime}'
                ORDER BY timestamp ASC";

        // 如果设置了限制，添加LIMIT子句
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
        }

        $result = Db::query($sql);

        // 移除raw_timestamp字段，只保留time和price
        foreach ($result as &$item) {
            unset($item['raw_timestamp']);
        }

        return $result;
    }



    /**
     * 获取批量价格数据的核心逻辑
     */
    private function getBatchPricesData(array $objectIds): array
    {
        // 使用最新价格表获取价格数据
        $pricesData = $this->getLatestPricesFromTable($objectIds);
        $latestPrices = $pricesData['current_prices'];
        $oldPrices = $pricesData['old_prices'];

        // 构建返回数据
        $result = [];
        foreach ($objectIds as $id) {
            $latestPrice = $latestPrices[$id] ?? 0;
            $oldPrice = $oldPrices[$id] ?? 0;
            $priceDifference = $latestPrice - $oldPrice;

            $result[$id] = [
                'price' => round($latestPrice, 2),
                'price_24h_ago' => round($priceDifference, 2)
            ];
        }

        return $result;
    }

    /**
     * 从数据库获取24小时前价格数据
     */
    private function getOldPricesFromDatabase(array $objectIds, string $time24hAgo): array
    {
        $sql = "SELECT p1.object_id, p1.price
                FROM " . Pricehistory::getTable() . " p1
                INNER JOIN (
                    SELECT object_id, MAX(timestamp) as max_timestamp
                    FROM " . Pricehistory::getTable() . "
                    WHERE object_id IN (" . implode(',', $objectIds) . ")
                    AND timestamp <= '{$time24hAgo}'
                    GROUP BY object_id
                ) p2 ON p1.object_id = p2.object_id AND p1.timestamp = p2.max_timestamp
                WHERE p1.object_id IN (" . implode(',', $objectIds) . ")
                AND p1.timestamp <= '{$time24hAgo}'";

        $oldPricesQuery = Db::query($sql);

        $oldPrices = [];
        foreach ($oldPricesQuery as $item) {
            $oldPrices[$item['object_id']] = round((float)$item['price'], 2);
        }

        return $oldPrices;
    }

    /**
     * 应用搜索条件 - 增强搜索功能
     */
    private function applySearchConditions($query, string $search): void
    {
        if (empty($search)) {
            return;
        }

        $search = trim($search);

        // 如果搜索词长度小于等于2个字符，使用精确匹配和开头匹配
        if (mb_strlen($search) <= 2) {
            $query->where(function($q) use ($search) {
                $q->where('i.object_name', $search)
                  ->whereOr('i.object_name', 'like', $search . '%')
                  ->whereOr('i.object_name', 'like', '%' . $search . '%');
            });
            return;
        }

        // 检查是否为纯数字或包含数字的搜索
        if (preg_match('/[\d.]+/', $search)) {
            $query->where(function($q) use ($search) {
                $q->where('i.object_name', 'like', '%' . $search . '%')
                  ->whereOr('i.object_id', 'like', '%' . $search);
                
                if (strpos($search, '.') !== false || preg_match('/\d+[xX]?/', $search)) {
                    $numericPart = preg_replace('/[^0-9.]/', '', $search);
                    if ($numericPart) {
                        $q->whereOr('i.object_name', 'like', '%' . $numericPart . '%');
                    }
                }
            });
            return;
        }

        // 中文搜索优化
        if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $search)) {
            $query->where(function($q) use ($search) {
                $q->where('i.object_name', 'like', '%' . $search . '%');
                
                $chars = mb_str_split($search);
                if (count($chars) > 1) {
                    $likePattern = '%' . implode('%', $chars) . '%';
                    $q->whereOr('i.object_name', 'like', $likePattern);
                }
            });
            return;
        }

        // 英文搜索优化
        if (preg_match('/^[a-zA-Z]+$/', $search)) {
            $query->where(function($q) use ($search) {
                $upperSearch = strtoupper($search);
                $lowerSearch = strtolower($search);
                
                $q->where('i.object_name', 'like', '%' . $search . '%')
                  ->whereOr('i.object_name', 'like', '%' . $upperSearch . '%')
                  ->whereOr('i.object_name', 'like', '%' . $lowerSearch . '%');
                
                if (strlen($search) > 2) {
                    $q->whereOr('i.object_name', 'regexp', '[[:<:]]' . $search . '[[:>:]]');
                }
            });
            return;
        }

        // 混合搜索（数字+字母+符号）
        $query->where(function($q) use ($search) {
            $q->where('i.object_name', 'like', '%' . $search . '%');
            
            $cleanSearch = preg_replace('/[^\w\d\x{4e00}-\x{9fa5}]/u', '', $search);
            if ($cleanSearch !== $search && !empty($cleanSearch)) {
                $q->whereOr('i.object_name', 'like', '%' . $cleanSearch . '%');
            }
            
            if (strpos($search, ' ') !== false) {
                $words = array_filter(explode(' ', $search));
                foreach ($words as $word) {
                    if (strlen($word) > 1) {
                        $q->whereOr('i.object_name', 'like', '%' . $word . '%');
                    }
                }
            }
        });
    }

    /**
     * 手动加载分类信息 - 高性能批量查询
     */
    private function loadCategoriesForItems(array &$items): void
    {
        if (empty($items)) {
            return;
        }

        // 收集所有需要的分类ID
        $categoryIds = array_unique(array_column($items, 'category_id'));
        
        // 批量查询分类信息 - 只查询必要字段
        $categories = CategoriesModel::whereIn('id', $categoryIds)
            ->field(['id', 'category_key', 'primary_class', 'second_class', 'second_class_cn'])
            ->column('*', 'id');

        // 将分类信息附加到物品数据
        foreach ($items as &$item) {
            $categoryId = $item['category_id'];
            $item['category'] = $categories[$categoryId] ?? null;
        }
    }

}
