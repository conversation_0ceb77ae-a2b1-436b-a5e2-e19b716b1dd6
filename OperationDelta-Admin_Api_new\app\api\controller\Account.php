<?php

namespace app\api\controller;

use ba\Date;
use Throwable;
use ba\Captcha;
use ba\Random;
use app\common\model\User;
use think\facade\Validate;
use app\common\facade\Token;
use app\common\model\UserScoreLog;
use app\common\model\UserMoneyLog;
use app\common\controller\Frontend;
use app\api\validate\Account as AccountValidate;
use app\common\model\sjz\Favorites;
use think\facade\Log;
use think\facade\Db;

class Account extends Frontend
{
    protected array $noNeedLogin = ['retrievePassword'];

    protected array $noNeedPermission = ['verification', 'changeBind'];
    
    

    public function initialize(): void
    {
        parent::initialize();
    }

    public function overview(): void
    {
        $sevenDays = Date::unixTime('day', -6);
        $score     = $money = $days = [];
        for ($i = 0; $i < 7; $i++) {
            $days[$i]    = date("Y-m-d", $sevenDays + ($i * 86400));
            $tempToday0  = strtotime($days[$i]);
            $tempToday24 = strtotime('+1 day', $tempToday0) - 1;
            $score[$i]   = UserScoreLog::where('user_id', $this->auth->id)
                ->where('create_time', 'BETWEEN', $tempToday0 . ',' . $tempToday24)
                ->sum('score');

            $userMoneyTemp = UserMoneyLog::where('user_id', $this->auth->id)
                ->where('create_time', 'BETWEEN', $tempToday0 . ',' . $tempToday24)
                ->sum('money');
            $money[$i]     = bcdiv($userMoneyTemp, 100, 2);
        }

        $this->success('', [
            'days'  => $days,
            'score' => $score,
            'money' => $money,
        ]);
    }

    /**
     * 会员资料
     * @throws Throwable
     */
    public function profile(): void
    {
        if ($this->request->isPost()) {
            // 禁止用户修改个人信息
            $this->error('禁止用户修改个人信息');
        }

        $this->success('', [
            'accountVerificationType' => get_account_verification_type()
        ]);
    }

    /**
     * 通过手机号或邮箱验证账户
     * 此处检查的验证码是通过 api/Ems或api/Sms发送的
     * 验证成功后，向前端返回一个 email-pass Token或着 mobile-pass Token
     * 在 changBind 方法中，通过 pass Token来确定用户已经通过了账户验证（用户未绑定邮箱/手机时通过账户密码验证）
     * @throws Throwable
     */
    public function verification(): void
    {
        $captcha = new Captcha();
        $params  = $this->request->only(['type', 'captcha']);
        if ($captcha->check($params['captcha'], ($params['type'] == 'email' ? $this->auth->email : $this->auth->mobile) . "user_{$params['type']}_verify")) {
            $uuid = Random::uuid();
            Token::set($uuid, $params['type'] . '-pass', $this->auth->id, 600);
            $this->success('', [
                'type'                     => $params['type'],
                'accountVerificationToken' => $uuid,
            ]);
        }
        $this->error(__('Please enter the correct verification code'));
    }

    /**
     * 修改绑定信息（手机号、邮箱）
     * 通过 pass Token来确定用户已经通过了账户验证，也就是以上的 verification 方法，同时用户未绑定邮箱/手机时通过账户密码验证
     * @throws Throwable
     */
    public function changeBind(): void
    {
        $captcha = new Captcha();
        $params  = $this->request->only(['type', 'captcha', 'email', 'mobile', 'accountVerificationToken', 'password']);
        $user    = $this->auth->getUser();

        if ($user[$params['type']]) {
            if (!Token::check($params['accountVerificationToken'], $params['type'] . '-pass', $user->id)) {
                $this->error(__('You need to verify your account before modifying the binding information'));
            }
        } elseif (!isset($params['password']) || $user->password != encrypt_password($params['password'], $user->salt)) {
            $this->error(__('Password error'));
        }

        // 检查验证码
        if ($captcha->check($params['captcha'], $params[$params['type']] . "user_change_{$params['type']}")) {
            if ($params['type'] == 'email') {
                $validate = Validate::rule(['email' => 'require|email|unique:user'])->message([
                    'email.require' => 'email format error',
                    'email.email'   => 'email format error',
                    'email.unique'  => 'email is occupied',
                ]);
                if (!$validate->check(['email' => $params['email']])) {
                    $this->error(__($validate->getError()));
                }
                $user->email = $params['email'];
            } elseif ($params['type'] == 'mobile') {
                $validate = Validate::rule(['mobile' => 'require|mobile|unique:user'])->message([
                    'mobile.require' => 'mobile format error',
                    'mobile.mobile'  => 'mobile format error',
                    'mobile.unique'  => 'mobile is occupied',
                ]);
                if (!$validate->check(['mobile' => $params['mobile']])) {
                    $this->error(__($validate->getError()));
                }
                $user->mobile = $params['mobile'];
            }
            Token::delete($params['accountVerificationToken']);
            $user->save();
            $this->success();
        }
        $this->error(__('Please enter the correct verification code'));
    }

    public function changePassword(): void
    {
        if ($this->request->isPost()) {
            $params = $this->request->only(['oldPassword', 'newPassword']);

            if (!$this->auth->checkPassword($params['oldPassword'])) {
                $this->error(__('Old password error'));
            }

            $model = $this->auth->getUser();
            $model->startTrans();
            try {
                $validate = new AccountValidate();
                $validate->scene('changePassword')->check(['password' => $params['newPassword']]);
                $model->resetPassword($this->auth->id, $params['newPassword']);
                $model->commit();
            } catch (Throwable $e) {
                $model->rollback();
                $this->error($e->getMessage());
            }

            $this->auth->logout();
            $this->success(__('Password has been changed, please login again~'));
        }
    }

    /**
     * 积分日志
     * @throws Throwable
     */
    public function integral(): void
    {
        $limit         = $this->request->request('limit');
        $integralModel = new UserScoreLog();
        $res           = $integralModel->where('user_id', $this->auth->id)
            ->order('create_time desc')
            ->paginate($limit);

        $this->success('', [
            'list'  => $res->items(),
            'total' => $res->total(),
        ]);
    }

    /**
     * 余额日志
     * @throws Throwable
     */
    public function balance(): void
    {
        $limit      = $this->request->request('limit');
        $moneyModel = new UserMoneyLog();
        $res        = $moneyModel->where('user_id', $this->auth->id)
            ->order('create_time desc')
            ->paginate($limit);

        $this->success('', [
            'list'  => $res->items(),
            'total' => $res->total(),
        ]);
    }

    /**
     * 找回密码
     * @throws Throwable
     */
    public function retrievePassword(): void
    {
        $params = $this->request->only(['type', 'account', 'captcha', 'password']);
        try {
            $validate = new AccountValidate();
            $validate->scene('retrievePassword')->check($params);
        } catch (Throwable $e) {
            $this->error($e->getMessage());
        }

        if ($params['type'] == 'email') {
            $user = User::where('email', $params['account'])->find();
        } else {
            $user = User::where('mobile', $params['account'])->find();
        }
        if (!$user) {
            $this->error(__('Account does not exist~'));
        }

        $captchaObj = new Captcha();
        if (!$captchaObj->check($params['captcha'], $params['account'] . 'user_retrieve_pwd')) {
            $this->error(__('Please enter the correct verification code'));
        }

        if ($user->resetPassword($user->id, $params['password'])) {
            $this->success(__('Password has been changed~'));
        } else {
            $this->error(__('Failed to modify password, please try again later~'));
        }
    }

    /**
     * 添加收藏
     * @throws Throwable
     */
    public function addFavorite(): void
    {
        // 检查用户是否登录
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
            return;
        }
        
        // 获取请求参数
        $params = $this->request->only(['object_id', 'object_type']);
        $objectId = $params['object_id'] ?? null;
        $objectType = $params['object_type'] ?? 'item';
        
        // 参数验证
        if (empty($objectId)) {
            $this->error('缺少必要参数: object_id');
            return;
        }
        
        // 检查是否已收藏
        $model = new \app\common\model\sjz\Favorites();
        $exists = $model->where([
            'user_id' => $this->auth->id,
            'object_id' => $objectId,
            'object_type' => $objectType
        ])->find();
        
        // 已收藏，直接返回成功
        if ($exists) {
            $this->success('已收藏');
            return;
        }
        
        // 未收藏，创建新记录
        $data = [
            'user_id' => $this->auth->id,
            'object_id' => $objectId,
            'object_type' => $objectType
        ];
        
        // 使用静态create方法创建
        if ($model::create($data)) {
            $this->success('收藏成功');
        } else {
            // 记录详细错误信息
            Log::error('收藏保存失败', [
                'data' => $data,
                'db_error' => Db::getLastSql()
            ]);
            $this->error('收藏失败');
        }
    }

    /**
     * 取消收藏
     * @throws Throwable
     */
    public function removeFavorite(): void
    {
        // 检查用户是否登录
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
            return;
        }
        
        // 获取请求参数
        $params = $this->request->only(['object_id', 'object_type']);
        $objectId = $params['object_id'] ?? null;
        $objectType = $params['object_type'] ?? 'item';
        
        // 参数验证
        if (empty($objectId)) {
            $this->error('缺少必要参数: object_id');
            return;
        }
        
        // 直接删除收藏记录
        $model = new \app\common\model\sjz\Favorites();
        $result = $model->where([
            'user_id' => $this->auth->id,
            'object_id' => $objectId,
            'object_type' => $objectType
        ])->delete(true); // true表示真实删除
        
        if ($result) {
            $this->success('取消收藏成功');
        } else {
            $this->success('未收藏');
        }
    }

    /**
     * 检查收藏状态
     * @throws Throwable
     */
    public function checkFavorite(): void
    {
        // 获取请求参数
        $params = $this->request->only(['object_id', 'object_type']);
        $objectId = $params['object_id'] ?? null;
        $objectType = $params['object_type'] ?? 'item';
        
        // 参数验证
        if (empty($objectId)) {
            $this->error('缺少必要参数: object_id');
            return;
        }
        
        // 未登录用户直接返回未收藏状态
        if (!$this->auth->isLogin()) {
            $this->success('', ['is_favorite' => false]);
            return;
        }

        try {
            // 查询收藏状态
            $model = new \app\common\model\sjz\Favorites();
            $exists = $model->where([
                'user_id' => $this->auth->id,
                'object_id' => $objectId,
                'object_type' => $objectType
            ])->find();

            $this->success('', ['is_favorite' => (bool)$exists]);
            
        } catch (\Exception $e) {
            Log::error('检查收藏状态异常', [
                'message' => $e->getMessage(),
                'user_id' => $this->auth->id,
                'object_id' => $objectId,
                'object_type' => $objectType,
                'trace' => $e->getTraceAsString()
            ]);
            
            // 出错时默认返回未收藏状态，避免前端处理异常
            $this->success('', ['is_favorite' => false]);
        }
    }

    /**
     * 批量检查收藏状态
     * @throws Throwable
     */
    public function batchCheckFavorites(): void
    {
        // 获取请求参数
        $objectIds = $this->request->request('object_ids', '');
        $objectType = $this->request->request('object_type', 'item');
        
        // 参数验证
        if (empty($objectIds)) {
            $this->error('缺少必要参数: object_ids');
            return;
        }
        
        // 将逗号分隔的ID转为数组
        $objectIdArray = explode(',', $objectIds);
        
        // 初始化结果数组，默认所有对象都未收藏
        $result = [];
        foreach ($objectIdArray as $id) {
            $result[$id] = false;
        }
        
        // 未登录用户直接返回未收藏状态
        if (!$this->auth->isLogin()) {
            $this->success('', ['favorites' => $result]);
            return;
        }

        try {
            // 查询当前用户已收藏的对象
            $model = new \app\common\model\sjz\Favorites();
            $favorites = $model->where([
                'user_id' => $this->auth->id,
                'object_type' => $objectType
            ])
            ->whereIn('object_id', $objectIdArray)
            ->column('object_id');
            
            // 更新结果数组中已收藏对象的状态
            foreach ($favorites as $favoriteId) {
                $result[$favoriteId] = true;
            }
            
            $this->success('', ['favorites' => $result]);
            
        } catch (\Exception $e) {
            // 出错时默认返回全部未收藏状态，避免前端处理异常
            $this->success('', ['favorites' => $result]);
        }
    }

    /**
     * 获取收藏列表
     */
    public function getFavorites(): void
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $page = $this->request->request('page', 1);
        $limit = $this->request->request('limit', 10);
        $type = $this->request->request('type', 'item');

        $model = new Favorites();
        $list = $model->with(['object'])
            ->where([
                'user_id' => $this->auth->id,
                'object_type' => $type,
            ])
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        $this->success('获取成功', [
            'list' => $list->items(),
            'total' => $list->total(),
            'last_page' => $list->lastPage()
        ]);
    }
}